#!/bin/sh

php artisan config:clear
php artisan cache:clear
composer install --no-interaction --no-scripts --no-progress

rm -rf public/storage
php artisan storage:link
# Run database migrations and seeders
php artisan migrate --force
php artisan migrate --force --database='pgsql' --path='database/migrations/pgsql'
php artisan migrate --force --database='pgsql_submissions' --path='database/migrations/pgsql_submissions'
php artisan filament:optimize
# Clear configuration cache

# Start supervisord
supervisord -c /etc/supervisor/conf.d/supervisord.conf
