{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "barryvdh/laravel-dompdf": "^2.0", "filament/filament": "^3.3", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^11.31", "laravel/helpers": "^1.5", "laravel/horizon": "^5.28", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.5", "league/csv": "^9.0", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "opcodesio/log-viewer": "^3.10", "predis/predis": "^2.2"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.9.1", "kitloong/laravel-migrations-generator": "^7.0", "larastan/larastan": "^2.0", "laravel/breeze": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "nunomaduro/phpinsights": "^2.11", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "pestphp/pest-plugin-type-coverage": "^2.8", "spatie/laravel-ignition": "^2.4", "wire-elements/wire-spy": "^0.0.9"}, "autoload": {"files": ["app/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "minimum-stability": "stable", "prefer-stable": true}