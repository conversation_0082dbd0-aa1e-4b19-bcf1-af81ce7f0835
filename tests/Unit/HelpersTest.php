<?php

it('passes a valid date', function () {
    $date = '20231031';
    $result = validate_date($date);

    expect($result)->toBeTrue();
});

it('fails an invalid date', function ($invalidDate) {
    $result = validate_date($invalidDate);

    expect($result)->toBeFalse();
})->with([
    '20231331', // Invalid Month
    '20231040', // Invalid day of month
    '20231O31', // String zero as part of Month
    '2023103I', // String I as part of day of month
    '2O231031', // String zero as part of the Year
    'abcdefgh', // Random string
    '202310311', // Longer than 8 characters
]);
