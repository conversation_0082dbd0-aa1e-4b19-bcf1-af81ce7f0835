<?php

use App\Enforcements\CBAEnforcements;
use App\Validators\CreditBorrowerAccount;

it('fails rule enf014 on pi code', function () {
    $validator = new CreditBorrowerAccount(['pi_identification_code' => null]);
    $validator->validate_pi_identification_code('CBA001');
    $errors = $validator->errors();

    expectEnforcementToFail($errors, 'CBA001', 'ENF014', CBAEnforcements::ENF014_RULE['title']);
});

it('fails rule enf068 on pi code', function () {
    $validator = new CreditBorrowerAccount(['pi_identification_code' => '999']);
    $validator->validate_pi_identification_code('CBA001');
    $errors = $validator->errors();

    expectEnforcementToFail($errors, 'CBA001', 'ENF068', CBAEnforcements::ENF068_RULE['title']);
});

it('fails rule enf130 on pi code', function () {
    $validator = new CreditBorrowerAccount(['pi_identification_code' => '~']);
    $validator->validate_pi_identification_code('CBA001');
    $errors = $validator->errors();

    expectEnforcementToFail($errors, 'CBA001', 'ENF130', CBAEnforcements::ENF130_RULE['title']);
});

it('fails rule enf014 on branch code', function () {
    $validator = new CreditBorrowerAccount(['branch_identification_code' => null]);
    $validator->validate_branch_identification_code('CBA002');

    expectEnforcementToFail(
        $validator->errors,
        'CBA002',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf116 on branch code', function () {
    $validator = new CreditBorrowerAccount([
        'branch_identification_code' => -98,
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code('CBA002');

    expectEnforcementToFail(
        $validator->errors,
        'CBA002',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf121 on branch code', function () {
    $validator = new CreditBorrowerAccount([
        'branch_identification_code' => 'ABC01',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code('CBA002');

    expectEnforcementToFail(
        $validator->errors,
        'CBA002',
        'ENF121',
        CBAEnforcements::ENF121_RULE['title']
    );
});

it('fails rule enf131 on branch code', function () {
    $validator = new CreditBorrowerAccount([
        'branch_identification_code' => 'ABC01',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code('CBA002');

    expectEnforcementToFail(
        $validator->errors,
        'CBA002',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf014 on borrowers client number', function () {
    $validator = new CreditBorrowerAccount([
        'borrowers_client_number' => null,
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_borrowers_client_number('CBA003');

    expectEnforcementToFail(
        $validator->errors,
        'CBA003',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf132 on borrowers client number', function () {
    $validator = new CreditBorrowerAccount([
        'borrowers_client_number' => '~',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_borrowers_client_number('CBA003');

    expectEnforcementToFail(
        $validator->errors,
        'CBA003',
        'ENF132',
        CBAEnforcements::ENF132_RULE['title']
    );
});

it('fails rule enf014 on borrower classification', function () {
    $validator = new CreditBorrowerAccount(['borrower_classification' => null]);
    $validator->validate_borrower_classification('CBA004');

    expectEnforcementToFail(
        $validator->errors,
        'CBA004',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf071 on borrower classification', function () {
    $validator = new CreditBorrowerAccount(['borrower_classification' => '2']);
    $validator->validate_borrower_classification('CBA004');

    expectEnforcementToFail($validator->errors, 'CBA004', 'ENF071', CBAEnforcements::ENF071_RULE['title']);

    $validator = new CreditBorrowerAccount(['borrower_classification' => '0']);
    $validator->validate_borrower_classification('CBA004');

    expect($validator->errors())->toBeEmpty();

    $validator = new CreditBorrowerAccount(['borrower_classification' => '1']);
    $validator->validate_borrower_classification('CBA004');

    expect($validator->errors)->toBeEmpty();
});

it('fails rule enf116 on borrower classification', function () {
    $validator = new CreditBorrowerAccount(['borrower_classification' => '-1']);
    $validator->validate_borrower_classification('CBA004');

    expectEnforcementToFail($validator->errors, 'CBA004', 'ENF116', CBAEnforcements::ENF116_RULE['title']);
});

it('fails rule enf131 on borrower classification', function () {
    $validator = new CreditBorrowerAccount(['borrower_classification' => 'abc']);
    $validator->validate_borrower_classification('CBA004');

    expectEnforcementToFail($validator->errors, 'CBA004', 'ENF131', CBAEnforcements::ENF131_RULE['title']);
});

it('fails rule enf134 on borrower classification', function () {
    $validator = new CreditBorrowerAccount(['borrower_classification' => '12']);
    $validator->validate_borrower_classification('CBA004');

    expectEnforcementToFail($validator->errors, 'CBA004', 'ENF134', CBAEnforcements::ENF134_RULE['title']);
});

it('fails rule enf014 on credit account reference', function () {
    $validator = new CreditBorrowerAccount(['credit_account_reference' => null]);
    $validator->validate_credit_account_reference();

    expectEnforcementToFail($validator->errors, 'CBA005', 'ENF014', CBAEnforcements::ENF014_RULE['title']);
});

it('fails rule enf130 on credit account reference', function () {
    $validator = new CreditBorrowerAccount(['credit_account_reference' => '~~~']);
    $validator->validate_credit_account_reference();

    expectEnforcementToFail($validator->errors, 'CBA005', 'ENF130', CBAEnforcements::ENF130_RULE['title']);
});

it('fails rule enf147 on credit account reference', function () {
    $validator = new CreditBorrowerAccount(['credit_account_reference' => \Illuminate\Support\Str::random(31)]);
    $validator->validate_credit_account_reference();

    expectEnforcementToFail($validator->errors, 'CBA005', 'ENF147', CBAEnforcements::ENF147_RULE['title']);
});

it('fails rule enf014 on credit account date', function () {
    $validator = new CreditBorrowerAccount(['credit_account_date' => null]);
    $validator->validate_credit_account_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA006',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('passes a valid credit account date', function () {
    $validator = new CreditBorrowerAccount(['credit_account_date' => '********']);
    $validator->validate_credit_account_date();

    expect($validator->errors)->toBeEmpty();
});

it('fails rule enf140 on credit account date', function () {
    $validator = new CreditBorrowerAccount(['credit_account_date' => '********1']);
    $validator->validate_credit_account_date();

    expectEnforcementToFail($validator->errors, 'CBA006', 'ENF140', CBAEnforcements::ENF140_RULE['title']);
});

it('fails rule enf131 on credit account date', function () {
    $validator = new CreditBorrowerAccount(['credit_account_date' => '2O231O31']);
    $validator->validate_credit_account_date();
    $errors = $validator->errors();

    expectEnforcementToFail($errors, 'CBA006', 'ENF007', CBAEnforcements::ENF007_RULE['title']);
    expectEnforcementToFail($errors, 'CBA006', 'ENF131', CBAEnforcements::ENF131_RULE['title']);
});

it('fails rule enf007 on credit account date', function () {
    $validator = new CreditBorrowerAccount(['credit_account_date' => '********']);
    $validator->validate_credit_account_date();

    expectEnforcementToFail($validator->errors, 'CBA006', 'ENF007', CBAEnforcements::ENF007_RULE['title']);

    $validator = new CreditBorrowerAccount(['credit_account_date' => '********']);
    $validator->validate_credit_account_date();

    expectEnforcementToFail($validator->errors, 'CBA006', 'ENF007', CBAEnforcements::ENF007_RULE['title']);
});

it('fails rule enf030 on credit amount', function ($accountType) {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => $accountType,
        'credit_amount' => null,
    ]);
    $validator->validate_credit_amount();

    expectEnforcementToFail($validator->errors, 'CBA007', 'ENF030', CBAEnforcements::ENF030_RULE['title']);
})->with(['6', '10', '11', '12']);

it('does not enforce rule enf030 on credit amount not required', function ($accountType) {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => $accountType,
        'credit_amount' => null,
    ]);
    $validator->validate_credit_amount();

    expect($validator->errors)->toBeEmpty();
})->with(['0', '1', '2', '3', '4', '5', '7', '8', '9', '13', '14', '15', '16']);

it('fails rule enf116 on credit amount', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => 6,
        'credit_amount' => '-10000',
    ]);
    $validator->validate_credit_amount();

    expectEnforcementToFail($validator->errors, 'CBA007', 'ENF116', CBAEnforcements::ENF116_RULE['title']);
});

it('fails rule enf133 on credit amount', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => 6,
        'credit_amount' => '1OOOO',
    ]);
    $validator->validate_credit_amount();

    expectEnforcementToFail($validator->errors, 'CBA007', 'ENF133', CBAEnforcements::ENF133_RULE['title']);

    $validator = new CreditBorrowerAccount([
        'credit_account_type' => 6,
        'credit_amount' => '1000O',
    ]);
    $validator->validate_credit_amount();

    expectEnforcementToFail($validator->errors, 'CBA007', 'ENF133', CBAEnforcements::ENF133_RULE['title']);
});

it('fails rule enf146 on credit amount', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => 6,
        'credit_amount' => '1************************',
    ]);
    $validator->validate_credit_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA007',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('fails rule enf161 on credit amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_ugx_equivalent' => null,
        'credit_account_type' => '6',
        'currency' => 'KES',
    ]);
    $validator->validate_credit_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->warnings,
        'CBA008',
        'ENF161',
        CBAEnforcements::ENF161_RULE['title']
    );
});

it('fails rule enf116 on credit amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_ugx_equivalent' => -10000,
    ]);
    $validator->validate_credit_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA008',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on credit amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_ugx_equivalent' => '1OOOOO', // 1 and letter O
    ]);
    $validator->validate_credit_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA008',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf146 on credit amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_ugx_equivalent' => '10101010101010101010101000',
    ]);
    $validator->validate_credit_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA008',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('fails rule enf029 on facility amount granted', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'facility_amount_granted' => null,
        'credit_account_type' => '4',
    ]);
    $validator->validate_facility_amount_granted();

    expectEnforcementToFail(
        $validator->errors,
        'CBA009',
        'ENF029',
        CBAEnforcements::ENF029_RULE['title']
    );
})->with(['4', '7', '9']);

it('fails rule enf116 on facility amount granted', function () {
    $validator = new CreditBorrowerAccount([
        'facility_amount_granted' => -100000,
    ]);
    $validator->validate_facility_amount_granted();

    expectEnforcementToFail(
        $validator->errors,
        'CBA009',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf133 on facility amount granted', function () {
    $validator = new CreditBorrowerAccount([
        'facility_amount_granted' => '1OOOOO',
    ]);
    $validator->validate_facility_amount_granted();

    expectEnforcementToFail(
        $validator->errors,
        'CBA009',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );
});

it('fails rule enf146 on facility amount granted', function () {
    $validator = new CreditBorrowerAccount([
        'facility_amount_granted' => '10101010101010101010101000',
    ]);
    $validator->validate_facility_amount_granted();

    expectEnforcementToFail(
        $validator->errors,
        'CBA009',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('fails rule enf029 on credit amount drawdown', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown' => null,
        'credit_account_type' => $creditAccountType,
    ]);
    $validator->validate_credit_amount_drawdown();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF029',
        CBAEnforcements::ENF029_RULE['title']
    );
})->with(['4', '7', '9']);

it('fails rule enf116 on credit amount drawdown', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown' => -100000,
    ]);
    $validator->validate_credit_amount_drawdown();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf133 on credit amount drawdown', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown' => '10000O',
    ]);
    $validator->validate_credit_amount_drawdown();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );
});

it('fails rule enf146 on credit amount drawdown', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown' => '10101010101010101010101000',
    ]);
    $validator->validate_credit_amount_drawdown();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('fails rule enf160 on credit amount drawdown ugx equivalent', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown_ugx_equivalent' => null,
        'credit_account_type' => $creditAccountType,
        'currency' => 'KES',
    ]);
    $validator->validate_credit_amount_drawdown_ugx_equivalent();

    expectEnforcementToFail(
        $validator->warnings,
        'CBA011',
        'ENF160',
        CBAEnforcements::ENF160_RULE['title']
    );
})->with(['4', '7', '9']);

it('fails rule enf116 on credit amount drawdown ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown_ugx_equivalent' => -100000,
    ]);
    $validator->validate_credit_amount_drawdown_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA011',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on credit amount drawdown ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown_ugx_equivalent' => '1OOOOO',
    ]);
    $validator->validate_credit_amount_drawdown_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA011',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf146 on credit amount drawdown ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amount_drawdown_ugx_equivalent' => '10101010101010101010101000',
    ]);
    $validator->validate_credit_amount_drawdown_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA011',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('fails rule enf014 on credit account type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => null,
    ]);
    $validator->validate_credit_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf061 on credit account type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => '99',
    ]);
    $validator->validate_credit_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF061',
        CBAEnforcements::ENF061_RULE['title']
    );
});

it('fails rule enf116 on credit account type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => -100000,
    ]);
    $validator->validate_credit_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on credit account type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_type' => '1OOOOO',
    ]);
    $validator->validate_credit_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA010',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf131 on group identification joint account number', function () {
    $validator = new CreditBorrowerAccount([
        'group_identification_joint_account_number' => '1~~~',
    ]);
    $validator->validate_group_identification_joint_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA011',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );
});

it('fails rule enf147 on group identification joint account number', function () {
    $validator = new CreditBorrowerAccount([
        'group_identification_joint_account_number' => '10101010101010101010101********0',
    ]);
    $validator->validate_group_identification_joint_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA011',
        'ENF147',
        CBAEnforcements::ENF147_RULE['title']
    );
});

it('fails rule enf014 on transaction date', function () {
    $validator = new CreditBorrowerAccount([
        'transaction_date' => null,
    ]);
    $validator->validate_transaction_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA012',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf006 on transaction date', function () {
    $validator = new CreditBorrowerAccount([
        'transaction_date' => '********',
        'credit_account_date' => '********',
    ]);
    $validator->validate_transaction_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA012',
        'ENF006',
        CBAEnforcements::ENF006_RULE['title']
    );
});

it('fails rule enf007 on transaction date', function () {
    $validator = new CreditBorrowerAccount([
        'transaction_date' => '********',
        'credit_account_date' => '********',
    ]);
    $validator->validate_transaction_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA012',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'transaction_date' => '********',
        'credit_account_date' => '********',
    ]);
    $validator->validate_transaction_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA012',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );
});

it('fails rule enf131 on transaction date', function () {
    $validator = new CreditBorrowerAccount([
        'transaction_date' => '1OOOOO',
        'credit_account_date' => '********',
    ]);
    $validator->validate_transaction_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA012',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf140 on transaction date', function () {
    $validator = new CreditBorrowerAccount([
        'transaction_date' => '*********',
        'credit_account_date' => '********',
    ]);
    $validator->validate_transaction_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA012',
        'ENF140',
        CBAEnforcements::ENF140_RULE['title']
    );
});

it('fails rule enf014 on currency', function () {
    $validator = new CreditBorrowerAccount([
        'currency' => null,
    ]);
    $validator->validate_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA013',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf050 on currency', function () {
    $validator = new CreditBorrowerAccount([
        'currency' => 'XXX',
    ]);
    $validator->validate_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA013',
        'ENF050',
        CBAEnforcements::ENF050_RULE['title']
    );
});

it('fails rule enf130 on currency', function () {
    $validator = new CreditBorrowerAccount([
        'currency' => '1~~~',
    ]);
    $validator->validate_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA013',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );
});

it('fails rule enf136 on currency', function () {
    $validator = new CreditBorrowerAccount([
        'currency' => 'UGXS',
    ]);
    $validator->validate_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA013',
        'ENF136',
        CBAEnforcements::ENF136_RULE['title']
    );
});

it('fails rule enf014 on opening balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'opening_balance_indicator' => null,
    ]);
    $validator->validate_opening_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA014',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf072 on opening balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'opening_balance_indicator' => '2',
    ]);
    $validator->validate_opening_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA014',
        'ENF072',
        CBAEnforcements::ENF072_RULE['title']
    );
});

it('fails rule enf116 on opening balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'opening_balance_indicator' => -1,
    ]);
    $validator->validate_opening_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA014',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on opening balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'opening_balance_indicator' => '1OOOO',
    ]);
    $validator->validate_opening_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA014',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf134 on opening balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'opening_balance_indicator' => '10',
    ]);
    $validator->validate_opening_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA014',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('fails rule enf014 on maturity date', function () {
    $validator = new CreditBorrowerAccount([
        'maturity_date' => null,
    ]);
    $validator->validate_maturity_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA015',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf007 on maturity date', function () {
    $validator = new CreditBorrowerAccount([
        'maturity_date' => '********',
    ]);
    $validator->validate_maturity_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA015',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'maturity_date' => '********',
    ]);
    $validator->validate_maturity_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA015',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );
});

it('fails rule enf131 on maturity date', function () {
    $validator = new CreditBorrowerAccount([
        'maturity_date' => '1OOOO',
    ]);
    $validator->validate_maturity_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA015',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf140 on maturity date', function () {
    $validator = new CreditBorrowerAccount([
        'maturity_date' => '*********',
    ]);
    $validator->validate_maturity_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA015',
        'ENF140',
        CBAEnforcements::ENF140_RULE['title']
    );
});

it('fails rule enf156 on type of interest', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => null,
        'credit_account_type' => $creditAccountType,
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToFail(
        $validator->errors,
        'CBA016',
        'ENF156',
        CBAEnforcements::ENF156_RULE['title']
    );
})->with(['1', '2']);

it('passes rule enf156 on type of interest', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => null,
        'credit_account_type' => $creditAccountType,
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToPass($validator->errors, 'CBA016', 'ENF156');
})->with(['0', '8']);

it('fails rule enf073 on type of interest', function () {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => '2',
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToFail(
        $validator->errors,
        'CBA016',
        'ENF073',
        CBAEnforcements::ENF073_RULE['title']
    );
});

it('passes rule enf073 on type of interest', function ($interestType) {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => $interestType,
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToPass(
        $validator->errors,
        'CBA016',
        'ENF073'
    );
})->with(['0', '1']);

it('fails rule enf116 on type of interest', function () {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => -1,
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToFail(
        $validator->errors,
        'CBA016',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('passes rule enf116, enf131, enf134 on type of interest', function () {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => '1',
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToPass(
        $validator->errors,
        'CBA016',
        ['ENF116', 'ENF131', 'ENF134']
    );
});

it('fails rule enf131 on type of interest', function () {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => 'O',
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToFail(
        $validator->errors,
        'CBA016',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf134 on type of interest', function () {
    $validator = new CreditBorrowerAccount([
        'type_of_interest' => '10',
    ]);
    $validator->validate_type_of_interest();

    expectEnforcementToFail(
        $validator->errors,
        'CBA016',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('fails rule enf156 on interest calculation method', function () {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => null,
        'credit_account_type' => '1',
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToFail(
        $validator->errors,
        'CBA017',
        'ENF156',
        CBAEnforcements::ENF156_RULE['title']
    );
});

it('passes rule enf156 on interest calculation method', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => null,
        'credit_account_type' => $creditAccountType,
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToPass(
        $validator->errors,
        'CBA017',
        'ENF156'
    );
})->with(['0', '8']);

it('fails rule enf074 on interest calculation method', function () {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => '2',
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToFail(
        $validator->errors,
        'CBA017',
        'ENF074',
        CBAEnforcements::ENF074_RULE['title']
    );
});

it('fails rule enf116 on interest calculation method', function () {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => -1,
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToFail(
        $validator->errors,
        'CBA017',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on interest calculation method', function () {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => 'I',
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToFail(
        $validator->errors,
        'CBA017',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf134 on interest calculation method', function () {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => '10',
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToFail(
        $validator->errors,
        'CBA017',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('passes rules on interest calculation method', function ($calculationMethod) {
    $validator = new CreditBorrowerAccount([
        'interest_calculation_method' => $calculationMethod,
    ]);

    $validator->validate_interest_calculation_method();

    expectEnforcementToPass(
        $validator->errors,
        'CBA017',
        ['ENF074', 'ENF131', 'ENF134', 'ENF156']
    );
})->with(['0', '1']);

it('passes rules on annual interest rate at disbursement', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_disbursement' => '21.49',
        'credit_account_type' => $creditAccountType,
    ]);

    $validator->validate_annual_interest_rate_at_disbursement();

    expectEnforcementToPass(
        $validator->errors,
        'CBA018',
        ['ENF116', 'ENF133', 'ENF141', 'ENF156']
    );
})->with(['0', '8']);

it('fails rule enf116 on annual interest rate at disbursement', function () {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_disbursement' => -1,
    ]);

    $validator->validate_annual_interest_rate_at_disbursement();

    expectEnforcementToFail(
        $validator->errors,
        'CBA018',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf133 on annual interest rate at disbursement', function () {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_disbursement' => '1O',
    ]);

    $validator->validate_annual_interest_rate_at_disbursement();

    expectEnforcementToFail(
        $validator->errors,
        'CBA018',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );
});

it('fails rule enf141 on annual interest rate at disbursement', function () {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_disbursement' => '18.********',
    ]);

    $validator->validate_annual_interest_rate_at_disbursement();

    expectEnforcementToFail(
        $validator->errors,
        'CBA018',
        'ENF141',
        CBAEnforcements::ENF141_RULE['title']
    );
});

it('fails rule enf156 on annual interest rate at disbursement', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_disbursement' => null,
        'credit_account_type' => $creditAccountType,
    ]);

    $validator->validate_annual_interest_rate_at_disbursement();

    expectEnforcementToFail(
        $validator->errors,
        'CBA018',
        'ENF156',
        CBAEnforcements::ENF156_RULE['title']
    );
})->with(['1', '9']);

it('passes rules on annual interest rate at reporting', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_reporting' => '21.49',
        'credit_account_type' => $creditAccountType,
    ]);

    $validator->validate_annual_interest_rate_at_reporting();

    expectEnforcementToPass(
        $validator->errors,
        'CBA019',
        ['ENF116', 'ENF133', 'ENF141', 'ENF156']
    );
})->with(['0', '8']);

it('fails rule enf116 on annual interest rate at reporting', function () {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_reporting' => -1,
    ]);

    $validator->validate_annual_interest_rate_at_reporting();

    expectEnforcementToFail(
        $validator->errors,
        'CBA019',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf133 on annual interest rate at reporting', function () {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_reporting' => '1O',
    ]);

    $validator->validate_annual_interest_rate_at_reporting();

    expectEnforcementToFail(
        $validator->errors,
        'CBA019',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );
});

it('fails rule enf141 on annual interest rate at reporting', function () {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_reporting' => '18.********',
    ]);

    $validator->validate_annual_interest_rate_at_reporting();

    expectEnforcementToFail(
        $validator->errors,
        'CBA019',
        'ENF141',
        CBAEnforcements::ENF141_RULE['title']
    );
});

it('fails rule enf156 on annual interest rate at reporting', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'annual_interest_rate_at_reporting' => null,
        'credit_account_type' => $creditAccountType,
    ]);

    $validator->validate_annual_interest_rate_at_reporting();

    expectEnforcementToFail(
        $validator->errors,
        'CBA019',
        'ENF156',
        CBAEnforcements::ENF156_RULE['title']
    );
})->with(['1', '9']);

it('allows null on date of first payment', function () {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => null,
    ]);

    $validator->validate_date_of_first_payment();

    expect($validator->errors)->toBeEmpty()
        ->and($validator->warnings)->toBeEmpty();
});

it('fails rule enf006 on date of first payment', function () {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => '********',
        'credit_account_date' => '********',
        'credit_account_type' => '3',
    ]);
    $validator->validate_date_of_first_payment();

    expectEnforcementToFail(
        $validator->errors,
        'CBA020',
        'ENF006',
        CBAEnforcements::ENF006_RULE['title']
    );
});

it('fails rule enf007 on date of first payment', function () {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => '0231030',
        'credit_account_date' => '********',
        'credit_account_type' => '3',
    ]);
    $validator->validate_date_of_first_payment();

    expectEnforcementToFail(
        $validator->errors,
        'CBA020',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );
});

it('fails rule enf039 on date of first payment', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => '********',
        'credit_account_date' => '********',
        'credit_account_type' => $creditAccountType,
    ]);
    $validator->validate_date_of_first_payment();

    expectEnforcementToFail(
        $validator->errors,
        'CBA020',
        'ENF039',
        CBAEnforcements::ENF039_RULE['title']
    );
})->with(['0', '3', '7', '8']);

it('fails rule enf116 on date of first payment', function () {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => '-********',
        'credit_account_date' => '********',
        'credit_account_type' => '0',
    ]);
    $validator->validate_date_of_first_payment();

    expectEnforcementToFail(
        $validator->errors,
        'CBA020',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on date of first payment', function () {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => '2O231O31',
        'credit_account_date' => '********',
        'credit_account_type' => '0',
    ]);
    $validator->validate_date_of_first_payment();

    expectEnforcementToFail(
        $validator->errors,
        'CBA020',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf140 on date of first payment', function () {
    $validator = new CreditBorrowerAccount([
        'date_of_first_payment' => '*********',
        'credit_account_date' => '********',
        'credit_account_type' => '0',
    ]);
    $validator->validate_date_of_first_payment();

    expectEnforcementToFail(
        $validator->errors,
        'CBA020',
        'ENF140',
        CBAEnforcements::ENF140_RULE['title']
    );
});

it('fails rule enf014 on credit amortization type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amortization_type' => null,
        'credit_account_type' => '1',
    ]);
    $validator->validate_credit_amortization_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA021',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf075 on credit amortization type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amortization_type' => '4',
    ]);
    $validator->validate_credit_amortization_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA021',
        'ENF075',
        CBAEnforcements::ENF075_RULE['title']
    );
});

it('fails rule enf116 on credit amortization type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amortization_type' => '-3',
    ]);
    $validator->validate_credit_amortization_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA021',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on credit amortization type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amortization_type' => 'O',
    ]);
    $validator->validate_credit_amortization_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA021',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf134 on credit amortization type', function () {
    $validator = new CreditBorrowerAccount([
        'credit_amortization_type' => '10',
    ]);
    $validator->validate_credit_amortization_type();

    expectEnforcementToFail(
        $validator->errors,
        'CBA021',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('fails rule enf058 on credit payment frequency', function () {
    $validator = new CreditBorrowerAccount([
        'credit_payment_frequency' => '99',
    ]);
    $validator->validate_credit_payment_frequency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA022',
        'ENF058',
        CBAEnforcements::ENF058_RULE['title']
    );
});

it('fails rule enf116 on credit payment frequency', function () {
    $validator = new CreditBorrowerAccount([
        'credit_payment_frequency' => '-1',
    ]);
    $validator->validate_credit_payment_frequency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA022',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on credit payment frequency', function () {
    $validator = new CreditBorrowerAccount([
        'credit_payment_frequency' => 'O',
    ]);
    $validator->validate_credit_payment_frequency();

    expectEnforcementToFail(
        $validator->errors,
        'CBA022',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on credit payment frequency', function () {
    $validator = new CreditBorrowerAccount([
        'credit_payment_frequency' => '0',
    ]);
    $validator->validate_credit_payment_frequency();

    expectEnforcementToPass(
        $validator->errors,
        'CBA022',
        ['ENF058', 'ENF116', 'ENF131']
    );
});

it('fails rule enf028 on number of payments', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'number_of_payments' => null,
        'credit_account_type' => $creditAccountType,
        'risk_classification_criteria' => '0',
    ]);
    $validator->validate_number_of_payments();

    expectEnforcementToFail(
        $validator->errors,
        'CBA023',
        'ENF028',
        CBAEnforcements::ENF028_RULE['title']
    );
})->with(['6', '10', '11']);

it('fails rule enf116 on number of payments', function () {
    $validator = new CreditBorrowerAccount([
        'number_of_payments' => '-1',
        'credit_payment_frequency' => '0',
    ]);
    $validator->validate_number_of_payments();

    expectEnforcementToFail(
        $validator->errors,
        'CBA023',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on number of payments', function () {
    $validator = new CreditBorrowerAccount([
        'number_of_payments' => 'O',
        'credit_payment_frequency' => '0',
    ]);
    $validator->validate_number_of_payments();

    expectEnforcementToFail(
        $validator->errors,
        'CBA023',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf137 on number of payments', function () {
    $validator = new CreditBorrowerAccount([
        'number_of_payments' => '10000',
        'credit_payment_frequency' => '0',
    ]);
    $validator->validate_number_of_payments();

    expectEnforcementToFail(
        $validator->errors,
        'CBA023',
        'ENF137',
        CBAEnforcements::ENF137_RULE['title']
    );
});

it('fails rule enf180 on number of payments', function () {
    $validator = new CreditBorrowerAccount([
        'number_of_payments' => '10',
        'credit_payment_frequency' => '1',
    ]);
    $validator->validate_number_of_payments();

    expectEnforcementToFail(
        $validator->errors,
        'CBA023',
        'ENF180',
        CBAEnforcements::ENF180_RULE['title']
    );
});

it('fails rule enf028 on monthly instalment amount', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'monthly_instalment_amount' => null,
        'credit_account_type' => $creditAccountType,
    ]);
    $validator->validate_monthly_installment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA024',
        'ENF028',
        CBAEnforcements::ENF028_RULE['title']
    );
})->with(['6', '10', '11']);

it('fails rule enf116 on monthly instalment amount', function () {
    $validator = new CreditBorrowerAccount([
        'monthly_instalment_amount' => '-100000',
    ]);
    $validator->validate_monthly_installment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA024',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf133 on monthly instalment amount', function () {
    $validator = new CreditBorrowerAccount([
        'monthly_instalment_amount' => '1000O',
    ]);
    $validator->validate_monthly_installment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA024',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );
});

it('fails rule enf146 on monthly instalment amount', function () {
    $validator = new CreditBorrowerAccount([
        'monthly_instalment_amount' => '110000100010001********01000',
    ]);
    $validator->validate_monthly_installment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA024',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('passes rules on monthly instalment amount', function () {
    $validator = new CreditBorrowerAccount([
        'monthly_instalment_amount' => '1100000.00',
    ]);
    $validator->validate_monthly_installment_amount();

    expectEnforcementToPass(
        $validator->errors,
        'CBA024',
        ['ENF116', 'ENF133', 'ENF146']
    );
});

it('fails rule enf014 on current balance amount', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount' => null,
    ]);
    $validator->validate_current_balance_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA025',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf112 on current balance amount', function ($creditAccountType) {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount' => 0,
        'credit_account_type' => $creditAccountType,
        'credit_account_status' => '3',
    ]);
    $validator->validate_current_balance_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA025',
        'ENF112',
        CBAEnforcements::ENF112_RULE['title']
    );
})->with(['6', '10', '11', '12']);

it('fails rule enf116 on current balance amount', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount' => '-20000',
        'credit_account_type' => '1',
        'credit_account_status' => '4',
    ]);
    $validator->validate_current_balance_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA025',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf120 on current balance amount', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount' => '20000.00',
        'credit_account_type' => '1',
        'credit_account_status' => '4',
    ]);
    $validator->validate_current_balance_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA025',
        'ENF120',
        CBAEnforcements::ENF120_RULE['title']
    );
});

it('fails rule enf133 on current balance amount', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount' => '2000O',
        'credit_account_type' => '1',
        'credit_account_status' => '0',
    ]);
    $validator->validate_current_balance_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA025',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );
});

it('fails rule enf146 on current balance amount', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount' => '200001********02000201000100000',
        'credit_account_type' => '1',
        'credit_account_status' => '0',
    ]);
    $validator->validate_current_balance_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA025',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('allows empty current balance amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount_ugx_equivalent' => null,
    ]);
    $validator->validate_current_balance_amount_ugx_equivalent();

    expect($validator->errors)
        ->toBeEmpty()
        ->and($validator->warnings)
        ->toBeEmpty();
});

it('fails rule enf116 on current balance amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount_ugx_equivalent' => '-20000',
    ]);
    $validator->validate_current_balance_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA026',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on current balance amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount_ugx_equivalent' => '2OOOO',
    ]);
    $validator->validate_current_balance_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA026',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf146 on current balance amount ugx equivalent', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_amount_ugx_equivalent' => '200001********02000201000100000',
    ]);
    $validator->validate_current_balance_amount_ugx_equivalent();

    expectEnforcementToFail(
        $validator->errors,
        'CBA026',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('fails rule enf014 on current balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_indicator' => null,
    ]);
    $validator->validate_current_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA027',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );
});

it('fails rule enf076 on current balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_indicator' => '2',
    ]);
    $validator->validate_current_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA027',
        'ENF076',
        CBAEnforcements::ENF076_RULE['title']
    );
});

it('fails rule enf116 on current balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_indicator' => '-1',
    ]);
    $validator->validate_current_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA027',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('fails rule enf131 on current balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_indicator' => 'I',
    ]);
    $validator->validate_current_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA027',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails rule enf134 on current balance indicator', function () {
    $validator = new CreditBorrowerAccount([
        'current_balance_indicator' => '10',
    ]);
    $validator->validate_current_balance_indicator();

    expectEnforcementToFail(
        $validator->errors,
        'CBA027',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('allows empty last payment date', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_date' => null,
    ]);
    $validator->validate_last_payment_date();

    expect($validator->errors)
        ->toBeEmpty()
        ->and($validator->warnings)
        ->toBeEmpty();
});

it('fails rule enf006 on last payment date', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_date' => '********',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'last_payment_amount' => '100000',
    ]);
    $validator->validate_last_payment_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA028',
        'ENF006',
        CBAEnforcements::ENF006_RULE['title']
    );
});

it('fails rule enf007 on last payment date', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_date' => '********0',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'last_payment_amount' => '100000',
    ]);
    $validator->validate_last_payment_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA028',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );
});

it('fails rule enf010 on last payment date', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_date' => '********',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'last_payment_amount' => null,
    ]);
    $validator->validate_last_payment_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA028',
        'ENF010',
        CBAEnforcements::ENF010_RULE['title']
    );
});

it('fails rule enf013 on last payment date', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_date' => '********',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'last_payment_amount' => null,
    ]);
    $validator->validate_last_payment_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA028',
        'ENF013',
        CBAEnforcements::ENF013_RULE['title']
    );
});

it('fails rule enf131 on last payment date', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_date' => '2O231101',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'last_payment_amount' => null,
    ]);
    $validator->validate_last_payment_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA028',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('fails enforcement rules on last payment amount', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_amount' => null,
        'date_of_first_payment' => '********',
        'last_payment_date' => '2O231023',
        'submission_date' => '********',
        'balance_overdue' => '0',
    ]);
    $validator->validate_last_payment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA029',
        'ENF008',
        CBAEnforcements::ENF008_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA029',
        'ENF032',
        CBAEnforcements::ENF032_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'last_payment_amount' => '-1000000.00',
    ]);
    $validator->validate_last_payment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA029',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'last_payment_amount' => '1000O',
    ]);
    $validator->validate_last_payment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA029',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'last_payment_amount' => '1****************001********0000',
    ]);
    $validator->validate_last_payment_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA029',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('passes enforcement rules on last payment amount', function () {
    $validator = new CreditBorrowerAccount([
        'last_payment_amount' => '1000000.00',
    ]);
    $validator->validate_last_payment_amount();

    expectEnforcementToPass(
        $validator->errors,
        'CBA029',
        ['ENF008', 'ENF032', 'ENF116', 'ENF133', 'ENF146']
    );
});

it('fails enforcement rules on credit_account_status', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_status' => null,
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_status' => '4',
        'balance_overdue' => '100000',
        'credit_account_closure_date' => null,
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF004',
        CBAEnforcements::ENF004_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF002',
        CBAEnforcements::ENF002_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_status' => '3',
        'current_balance_amount' => '0',
        'balance_overdue' => '0',
        'credit_account_closure_date' => null,
        'credit_account_arrears_date' => '',
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF001',
        CBAEnforcements::ENF001_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_status' => '10',
        'current_balance_amount' => '0',
        'balance_overdue' => '0',
        'credit_account_closure_date' => null,
        'credit_account_arrears_date' => '',
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF044',
        CBAEnforcements::ENF044_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_status' => '-1',
        'current_balance_amount' => '0',
        'balance_overdue' => '0',
        'credit_account_closure_date' => null,
        'credit_account_arrears_date' => '',
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_status' => 'I',
        'current_balance_amount' => '0',
        'balance_overdue' => '0',
        'credit_account_closure_date' => null,
        'credit_account_arrears_date' => '',
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToFail(
        $validator->errors,
        'CBA030',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on credit_account_status', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_status' => '1',
        'balance_overdue' => '0',
    ]);
    $validator->validate_credit_account_status();

    expectEnforcementToPass(
        $validator->errors,
        'CBA030',
        ['ENF001', 'ENF002', 'ENF004', 'ENF014', 'ENF044', 'ENF116', 'ENF131', '134']
    );
});

it('failes enforcement rules on last_status_change_date', function () {
    $validator = new CreditBorrowerAccount([
        'last_status_change_date' => null,
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA031',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'last_status_change_date' => '2023410',
        'submission_date' => '********',
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA031',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'last_status_change_date' => '********',
        'submission_date' => '********',
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA031',
        'ENF005',
        CBAEnforcements::ENF005_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'last_status_change_date' => '********0',
        'submission_date' => '********',
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA031',
        'ENF140',
        CBAEnforcements::ENF140_RULE['title']
    );
});

it('passes enforcement rules on last_status_change_date', function () {
    $validator = new CreditBorrowerAccount([
        'last_status_change_date' => '********',
        'submission_date' => '********',
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToPass(
        $validator->errors,
        'CBA031',
        ['ENF005', 'ENF007', 'ENF014', 'ENF140']
    );
});

it('failes enforcement rules on credit_account_risk_classification', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_risk_classification' => null,
    ]);
    $validator->validate_credit_account_risk_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CBA032',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_risk_classification' => '99',
    ]);
    $validator->validate_credit_account_risk_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CBA032',
        'ENF045',
        CBAEnforcements::ENF045_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA032',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_risk_classification' => '-1',
    ]);
    $validator->validate_credit_account_risk_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CBA032',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_risk_classification' => 'I',
    ]);
    $validator->validate_credit_account_risk_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CBA032',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on credit_account_risk_classification', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_risk_classification' => '1',
    ]);
    $validator->validate_credit_account_risk_classification();

    expectEnforcementToPass(
        $validator->errors,
        'CBA032',
        ['ENF014', 'ENF045', 'ENF116', 'ENF131', 'ENF134']
    );
});

it('fails enforcement rules on credit_account_arrears_date', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_arrears_date' => null,
        'credit_account_date' => '********',
        'submission_date' => '********',
        'date_of_first_payment' => null,
        'credit_account_type' => '1',
        'credit_account_status' => '1',
        'balance_overdue' => null,
        'last_payment_amount' => null,
    ]);
    $validator->validate_credit_account_arrears_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF091',
        CBAEnforcements::ENF091_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_arrears_date' => '********',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'date_of_first_payment' => null,
        'credit_account_type' => '1',
        'credit_account_status' => '1',
        'balance_overdue' => null,
        'last_payment_amount' => null,
    ]);
    $validator->validate_credit_account_arrears_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF005',
        CBAEnforcements::ENF005_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF006',
        CBAEnforcements::ENF006_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_arrears_date' => '********0',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'date_of_first_payment' => null,
        'credit_account_type' => '1',
        'credit_account_status' => '1',
        'balance_overdue' => null,
        'last_payment_amount' => null,
    ]);
    $validator->validate_credit_account_arrears_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF140',
        CBAEnforcements::ENF140_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_arrears_date' => '********',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'date_of_first_payment' => null,
        'credit_account_type' => '1',
        'credit_account_status' => '5',
        'balance_overdue' => null,
        'last_payment_amount' => null,
    ]);
    $validator->validate_credit_account_arrears_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF009',
        CBAEnforcements::ENF009_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_arrears_date' => '202311O1',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'date_of_first_payment' => null,
        'credit_account_type' => '1',
        'credit_account_status' => '5',
        'balance_overdue' => null,
        'last_payment_amount' => null,
    ]);
    $validator->validate_credit_account_arrears_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA033',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on credit_account_arrears_date', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_arrears_date' => '********',
        'credit_account_date' => '********',
        'submission_date' => '********',
        'date_of_first_payment' => null,
        'credit_account_type' => '1',
        'credit_account_status' => '1',
        'balance_overdue' => null,
        'last_payment_amount' => null,
    ]);
    $validator->validate_credit_account_arrears_date();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();
});

it('fails enforcement rules on number_of_days_in_arrears', function () {
    $validator = new CreditBorrowerAccount([
        'number_of_days_in_arrears' => null,
        'credit_account_arrears_date' => '********0',
        'balance_overdue' => 0,
    ]);
    $validator->validate_number_of_days_in_arrears();

    expectEnforcementToFail(
        $validator->errors,
        'CBA034',
        'ENF017',
        CBAEnforcements::ENF017_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'number_of_days_in_arrears' => '-1',
    ]);
    $validator->validate_number_of_days_in_arrears();

    expectEnforcementToFail(
        $validator->errors,
        'CBA034',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'number_of_days_in_arrears' => 'I',
    ]);
    $validator->validate_number_of_days_in_arrears();

    expectEnforcementToFail(
        $validator->errors,
        'CBA034',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'number_of_days_in_arrears' => '***********',
    ]);
    $validator->validate_number_of_days_in_arrears();

    expectEnforcementToFail(
        $validator->errors,
        'CBA034',
        'ENF142',
        CBAEnforcements::ENF142_RULE['title']
    );
});

it('passes rules on number_of_days_in_arrears', function () {
    $validator = new CreditBorrowerAccount([
        'number_of_days_in_arrears' => '100',
    ]);
    $validator->validate_number_of_days_in_arrears();

    expectEnforcementToPass(
        $validator->errors,
        'CBA034',
        ['ENF116', 'ENF131', 'ENF142']
    );
});

it('fails enforcement rules on balance_overdue', function () {
    $validator = new CreditBorrowerAccount([
        'balance_overdue' => '0',
        'credit_account_arrears_date' => '********',
        'number_of_days_in_arrears' => '10',
    ]);
    $validator->validate_balance_overdue();

    expectEnforcementToFail(
        $validator->errors,
        'CBA035',
        'ENF018',
        CBAEnforcements::ENF018_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'balance_overdue' => '1OO',
        'credit_account_arrears_date' => '********',
        'number_of_days_in_arrears' => '10',
    ]);
    $validator->validate_balance_overdue();

    expectEnforcementToFail(
        $validator->errors,
        'CBA035',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'balance_overdue' => '1000001010100101********00',
        'credit_account_arrears_date' => '********',
        'number_of_days_in_arrears' => '10',
    ]);
    $validator->validate_balance_overdue();

    expectEnforcementToFail(
        $validator->errors,
        'CBA035',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('passes rules on balance_overdue', function () {
    $validator = new CreditBorrowerAccount([
        'balance_overdue' => '100000',
        'credit_account_arrears_date' => '********',
        'number_of_days_in_arrears' => '10',
    ]);
    $validator->validate_balance_overdue();

    expect($validator->errors)->toBeEmpty();
});

it('fails enforcement rules on flag_for_restructured_credit', function () {
    $validator = new CreditBorrowerAccount([
        'flag_for_restructured_credit' => null,
    ]);
    $validator->validate_flag_for_restructured_credit();

    expectEnforcementToFail(
        $validator->errors,
        'CBA036',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'flag_for_restructured_credit' => '2',
    ]);
    $validator->validate_flag_for_restructured_credit();

    expectEnforcementToFail(
        $validator->errors,
        'CBA036',
        'ENF077',
        CBAEnforcements::ENF077_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'flag_for_restructured_credit' => '-1',
    ]);
    $validator->validate_flag_for_restructured_credit();

    expectEnforcementToFail(
        $validator->errors,
        'CBA036',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'flag_for_restructured_credit' => 'I',
    ]);
    $validator->validate_flag_for_restructured_credit();

    expectEnforcementToFail(
        $validator->errors,
        'CBA036',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'flag_for_restructured_credit' => '10',
    ]);
    $validator->validate_flag_for_restructured_credit();

    expectEnforcementToFail(
        $validator->errors,
        'CBA036',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('passes rules on flag_for_restructured_credit', function () {
    $validator = new CreditBorrowerAccount([
        'flag_for_restructured_credit' => '1',
    ]);
    $validator->validate_flag_for_restructured_credit();

    expectEnforcementToPass(
        $validator->errors,
        'CBA036',
        ['ENF116', 'ENF131', 'ENF134']
    );
});

it('passes rules on old_branch_code', function () {
    $validator = new CreditBorrowerAccount([
        'old_branch_code' => '110',
    ]);
    $validator->validate_old_branch_code();

    expectEnforcementToPass(
        $validator->errors,
        'CBA037',
        ['ENF116', 'ENF131']
    );
});

it('fails enforcement rules on old_branch_code', function () {
    $validator = new CreditBorrowerAccount([
        'old_branch_code' => '-100',
    ]);
    $validator->validate_old_branch_code();

    expectEnforcementToFail(
        $validator->errors,
        'CBA037',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'old_branch_code' => '1OO',
    ]);
    $validator->validate_old_branch_code();

    expectEnforcementToFail(
        $validator->errors,
        'CBA037',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on old_account_number', function () {
    $validator = new CreditBorrowerAccount([
        'old_account_number' => 'AC101038494',
    ]);
    $validator->validate_old_account_number();

    expectEnforcementToPass(
        $validator->errors,
        'CBA038',
        ['ENF130', 'ENF147']
    );
});

it('failes enforcement rules on old_account_number', function () {
    $validator = new CreditBorrowerAccount([
        'old_account_number' => 'AC101038494~',
    ]);
    $validator->validate_old_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA038',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'old_account_number' => '***********************0900009009000',
    ]);
    $validator->validate_old_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA038',
        'ENF147',
        CBAEnforcements::ENF147_RULE['title']
    );
});

it('passes rules on old_client_number', function () {
    $validator = new CreditBorrowerAccount([
        'old_client_number' => '***********************',
    ]);
    $validator->validate_old_client_number();

    expectEnforcementToPass(
        $validator->errors,
        'CBA039',
        ['ENF130', 'ENF147']
    );
});

it('fails enforcement rules on old_client_number', function () {
    $validator = new CreditBorrowerAccount([
        'old_client_number' => null,
        'flag_for_restructured_credit' => '0',
    ]);
    $validator->validate_old_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA039',
        'ENF015',
        CBAEnforcements::ENF015_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'old_client_number' => 'AC1000988~',
    ]);
    $validator->validate_old_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA039',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'old_client_number' => '***********************0900009009000',
    ]);
    $validator->validate_old_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CBA039',
        'ENF147',
        CBAEnforcements::ENF147_RULE['title']
    );
});

it('passes rules on old_pi_identification_code', function () {
    $validator = new CreditBorrowerAccount([
        'old_pi_identification_code' => 'CB005',
    ]);
    $validator->validate_old_pi_identification_code();

    expectEnforcementToPass(
        $validator->errors,
        'CBA040',
        ['ENF068', 'ENF130']
    );
});

it('fails enforcement rules on old_pi_identification_code', function () {
    $validator = new CreditBorrowerAccount([
        'old_pi_identification_code' => 'CB9999',
    ]);
    $validator->validate_old_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CBA040',
        'ENF068',
        CBAEnforcements::ENF068_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'old_pi_identification_code' => 'CB999~',
    ]);
    $validator->validate_old_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CBA040',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'old_pi_identification_code' => 'CB000005',
    ]);
    $validator->validate_old_pi_identification_code();
});

it('passes rules on credit_account_closure_date', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_closure_date' => '********',
        'submission_date' => '********',
    ]);
    $validator->validate_credit_account_closure_date();

    expectEnforcementToPass(
        $validator->errors,
        'CBA041',
        ['ENF068', 'ENF130']
    );
});

it('fails enforcement rules on credit_account_closure_date', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_closure_date' => null,
        'submission_date' => '********',
        'credit_account_status' => '3',
        'credit_account_closure_reason' => '5',
    ]);
    $validator->validate_credit_account_closure_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA041',
        'ENF020',
        CBAEnforcements::ENF020_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA041',
        'ENF034',
        CBAEnforcements::ENF034_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_date' => '********',
        'submission_date' => '********',
    ]);
    $validator->validate_credit_account_closure_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA041',
        'ENF005',
        CBAEnforcements::ENF005_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_date' => '********0',
        'submission_date' => '********',
    ]);
    $validator->validate_credit_account_closure_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA041',
        'ENF007',
        CBAEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA041',
        'ENF140',
        CBAEnforcements::ENF140_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_date' => '20231O31',
        'submission_date' => '********',
    ]);
    $validator->validate_credit_account_closure_date();

    expectEnforcementToFail(
        $validator->errors,
        'CBA041',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on credit_account_closure_reason', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_closure_reason' => null,
        'credit_account_closure_date' => null,
        'credit_account_status' => '1',
    ]);
    $validator->validate_credit_account_closure_reason();

    expect($validator->errors)->toBeEmpty();

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_reason' => '0',
        'credit_account_closure_date' => null,
    ]);
    $validator->validate_credit_account_closure_reason();

    expectEnforcementToPass(
        $validator->errors,
        'CBA042',
        ['ENF046', 'ENF116', 'ENF131', 'ENF134']
    );
});

it('fails enforcement rules on credit_account_closure_reason', function () {
    $validator = new CreditBorrowerAccount([
        'credit_account_closure_reason' => '99',
        'credit_account_closure_date' => null,
    ]);
    $validator->validate_credit_account_closure_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CBA042',
        'ENF046',
        CBAEnforcements::ENF046_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA042',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_reason' => null,
        'credit_account_closure_date' => '********',
        'credit_account_status' => '3',
    ]);
    $validator->validate_credit_account_closure_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CBA042',
        'ENF033',
        CBAEnforcements::ENF033_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA042',
        'ENF062',
        CBAEnforcements::ENF062_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_reason' => '-1',
        'credit_account_closure_date' => null,
    ]);
    $validator->validate_credit_account_closure_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CBA042',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'credit_account_closure_reason' => 'O',
        'credit_account_closure_date' => null,
    ]);
    $validator->validate_credit_account_closure_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CBA042',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on specific_provision_amount', function () {
    $validator = new CreditBorrowerAccount([
        'specific_provision_amount' => null,
        'credit_account_risk_classification' => null,
    ]);
    $validator->validate_specific_provision_amount();

    expect($validator->errors)->toBeEmpty();

    $validator = new CreditBorrowerAccount([
        'specific_provision_amount' => '1000000.00',
        'credit_account_risk_classification' => '1',
    ]);
    $validator->validate_specific_provision_amount();

    expectEnforcementToPass(
        $validator->errors,
        'CBA043',
        ['ENF116', 'ENF119', 'ENF133', 'ENF146']
    );
});

it('fails enforcement rules on specific_provision_amount', function () {
    $validator = new CreditBorrowerAccount([
        'specific_provision_amount' => '-1',
        'credit_account_risk_classification' => '1',
    ]);
    $validator->validate_specific_provision_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA043',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA043',
        'ENF119',
        CBAEnforcements::ENF119_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'specific_provision_amount' => '1O',
        'credit_account_risk_classification' => '1',
    ]);
    $validator->validate_specific_provision_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA043',
        'ENF133',
        CBAEnforcements::ENF133_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'specific_provision_amount' => '1********00001********00100000',
        'credit_account_risk_classification' => '1',
    ]);
    $validator->validate_specific_provision_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA043',
        'ENF146',
        CBAEnforcements::ENF146_RULE['title']
    );
});

it('passes rules on client_consent_flag', function () {
    $validator = new CreditBorrowerAccount([
        'client_consent_flag' => 'Y',
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToPass(
        $validator->errors,
        'CBA044',
        ['ENF014', 'ENF078', 'ENF130']
    );
});

it('fails enforcement rules on client_consent_flag', function () {
    $validator = new CreditBorrowerAccount([
        'client_consent_flag' => null,
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CBA044',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'client_consent_flag' => 'O',
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CBA044',
        'ENF078',
        CBAEnforcements::ENF078_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'client_consent_flag' => '~',
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CBA044',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on client_advice_notice_flag', function () {
    $validator = new CreditBorrowerAccount([
        'client_advice_notice_flag' => 'Y',
    ]);
    $validator->validate_client_advice_notice_flag();

    expectEnforcementToPass(
        $validator->errors,
        'CBA045',
        ['ENF014', 'ENF078', 'ENF130']
    );
});

it('fails enforcement rules on client_advice_notice_flag', function () {
    $validator = new CreditBorrowerAccount([
        'client_advice_notice_flag' => null,
    ]);
    $validator->validate_client_advice_notice_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CBA045',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'client_advice_notice_flag' => 'O',
    ]);
    $validator->validate_client_advice_notice_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CBA045',
        'ENF078',
        CBAEnforcements::ENF078_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'client_advice_notice_flag' => '~',
    ]);
    $validator->validate_client_advice_notice_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CBA045',
        'ENF130',
        CBAEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on term', function () {
    $validator = new CreditBorrowerAccount([
        'term' => '1',
    ]);
    $validator->validate_term();

    expectEnforcementToPass(
        $validator->errors,
        'CBA046',
        ['ENF014', 'ENF116', 'ENF131', 'ENF138']
    );
});

it('fails enforcement rules on term', function () {
    $validator = new CreditBorrowerAccount([
        'term' => null,
    ]);
    $validator->validate_term();

    expectEnforcementToFail(
        $validator->errors,
        'CBA046',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'term' => '-1',
    ]);
    $validator->validate_term();

    expectEnforcementToFail(
        $validator->errors,
        'CBA046',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'term' => 'O',
    ]);
    $validator->validate_term();

    expectEnforcementToFail(
        $validator->errors,
        'CBA046',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'term' => '********',
    ]);
    $validator->validate_term();

    expectEnforcementToFail(
        $validator->errors,
        'CBA046',
        'ENF138',
        CBAEnforcements::ENF138_RULE['title']
    );
});

it('passes rules on loan_purpose', function () {
    $validator = new CreditBorrowerAccount([
        'loan_purpose' => '1',
    ]);
    $validator->validate_loan_purpose();

    expect($validator->errors)->toBeEmpty();
});

it('fails enforcement rules on loan_purpose', function () {
    $validator = new CreditBorrowerAccount([
        'loan_purpose' => null,
    ]);
    $validator->validate_loan_purpose();

    expectEnforcementToFail(
        $validator->errors,
        'CBA047',
        'ENF014',
        CBAEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'loan_purpose' => 'O',
    ]);
    $validator->validate_loan_purpose();

    expectEnforcementToFail(
        $validator->errors,
        'CBA047',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'loan_purpose' => '********',
    ]);
    $validator->validate_loan_purpose();

    expectEnforcementToFail(
        $validator->errors,
        'CBA047',
        'ENF138',
        CBAEnforcements::ENF138_RULE['title']
    );
});

it('passes rules on group_joint_account_exposure_amount', function () {
    $validator = new CreditBorrowerAccount([
        'group_joint_account_exposure_amount' => '1',
    ]);
    $validator->validate_group_joint_account_exposure_amount();

    expectEnforcementToPass(
        $validator->errors,
        'CBA048',
        ['ENF116']
    );

    $validator = new CreditBorrowerAccount([
        'group_joint_account_exposure_amount' => null,
        'group_identification_joint_account_number' => '********',
        'flag_for_group' => '1',
        'flag_for_joint_account' => '1',
    ]);
    $validator->validate_group_joint_account_exposure_amount();

    expectEnforcementToPass(
        $validator->errors,
        'CBA048',
        ['ENF169', 'ENF170']
    );
});

it('fails enforcement rules on group_joint_account_exposure_amount', function () {
    $validator = new CreditBorrowerAccount([
        'group_joint_account_exposure_amount' => null,
        'group_identification_joint_account_number' => null,
        'flag_for_group' => 0,
        'flag_for_joint_account' => 0,
    ]);
    $validator->validate_group_joint_account_exposure_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA048',
        'ENF169',
        CBAEnforcements::ENF169_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA048',
        'ENF170',
        CBAEnforcements::ENF170_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'group_joint_account_exposure_amount' => '-1000000',
    ]);
    $validator->validate_group_joint_account_exposure_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CBA048',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
});

it('passes rules on flag_for_group', function () {
    $validator = new CreditBorrowerAccount([
        'flag_for_group' => null,
    ]);
    $validator->validate_flag_for_group();

    expect($validator->errors)->toBeEmpty();
});

it('fails enforcement rules on flag_for_group', function () {
    $validator = new CreditBorrowerAccount([
        'flag_for_group' => '1O',
    ]);
    $validator->validate_flag_for_group();

    expectEnforcementToFail(
        $validator->errors,
        'CBA049',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA049',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('passes rules on flag_for_joint_account', function () {
    $validator = new CreditBorrowerAccount([
        'flag_for_joint_account' => null,
    ]);
    $validator->validate_flag_for_joint_account();

    expect($validator->errors)->toBeEmpty();
});

it('fails enforcement rules on flag_for_joint_account', function () {
    $validator = new CreditBorrowerAccount([
        'flag_for_joint_account' => '1O',
    ]);
    $validator->validate_flag_for_joint_account();

    expectEnforcementToFail(
        $validator->errors,
        'CBA050',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA050',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );
});

it('passes rules on mode_of_restructure', function () {
    $validator = new CreditBorrowerAccount([
        'mode_of_restructure' => null,
        'flag_for_restructured_credit' => '1',
    ]);
    $validator->validate_mode_of_restructure();

    expect($validator->errors)->toBeEmpty();

    $validator = new CreditBorrowerAccount([
        'mode_of_restructure' => '1',
        'flag_for_restructured_credit' => '1',
    ]);
    $validator->validate_mode_of_restructure();

    expect($validator->errors)->toBeEmpty();
});

it('fails enforcement rules on mode_of_restructure', function () {
    $validator = new CreditBorrowerAccount([
        'mode_of_restructure' => null,
        'flag_for_restructured_credit' => '0',
    ]);
    $validator->validate_mode_of_restructure();

    expectEnforcementToFail(
        $validator->errors,
        'CBA059',
        'ENF184',
        CBAEnforcements::ENF184_RULE['title']
    );
});

it('passes rules on risk_classification_criteria', function () {
    $validator = new CreditBorrowerAccount([
        'risk_classification_criteria' => null,
    ]);
    $validator->validate_risk_classification_criteria();

    expect($validator->errors)->toBeEmpty();

    $validator = new CreditBorrowerAccount([
        'risk_classification_criteria' => '0',
    ]);
    $validator->validate_risk_classification_criteria();

    expectEnforcementToPass(
        $validator->errors,
        'CBA060',
        ['ENF045', 'ENF116', 'ENF131', 'ENF134']
    );
});

it('fails enforcement rules on risk_classification_criteria', function () {
    $validator = new CreditBorrowerAccount([
        'risk_classification_criteria' => '2',
    ]);
    $validator->validate_risk_classification_criteria();

    expectEnforcementToFail(
        $validator->errors,
        'CBA060',
        'ENF045',
        CBAEnforcements::ENF045_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'risk_classification_criteria' => '-10',
    ]);
    $validator->validate_risk_classification_criteria();

    expectEnforcementToFail(
        $validator->errors,
        'CBA060',
        'ENF116',
        CBAEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CBA060',
        'ENF134',
        CBAEnforcements::ENF134_RULE['title']
    );

    $validator = new CreditBorrowerAccount([
        'risk_classification_criteria' => 'O',
    ]);
    $validator->validate_risk_classification_criteria();

    expectEnforcementToFail(
        $validator->errors,
        'CBA060',
        'ENF131',
        CBAEnforcements::ENF131_RULE['title']
    );
});
