<?php

use App\Enforcements\PISEnforcements;
use App\Validators\ParticipatingInstitutionStakeholders;

it('passes rules for pi_identification_code', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'pi_identification_code' => 'CB005',
    ]);

    $validator->validate_pi_identification_code();

    expectEnforcementToPass($validator->errors, 'PIS001', ['ENF014', 'ENF068', 'ENF130']);
});

it('fails enforcements rules on pis_identification_code', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'pi_identification_code' => null,
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'PIS001',
        'ENF014',
        PISEnforcements::ENF014_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'pi_identification_code' => 'CB999',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'PIS001',
        'ENF068',
        PISEnforcements::ENF068_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'pi_identification_code' => 'CB9909~',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'PIS001',
        'ENF130',
        PISEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on stakeholder_type', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_type' => '1',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_stakeholder_type();

    expectEnforcementToPass(
        $validator->errors,
        'PIS002',
        ['ENF014', 'ENF071', 'ENF116', 'ENF131', 'ENF134']
    );
});

it('failes rules on stakeholder_type', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_type' => null,
    ]);
    $validator->validate_stakeholder_type();

    expectEnforcementToFail(
        $validator->errors,
        'PIS002',
        'ENF014',
        PISEnforcements::ENF014_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_type' => '10',
    ]);
    $validator->validate_stakeholder_type();

    expectEnforcementToFail(
        $validator->errors,
        'PIS002',
        'ENF071',
        PISEnforcements::ENF071_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PIS002',
        'ENF134',
        PISEnforcements::ENF134_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_type' => '-1',
    ]);
    $validator->validate_stakeholder_type();

    expectEnforcementToFail(
        $validator->errors,
        'PIS002',
        'ENF116',
        PISEnforcements::ENF116_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_type' => 'O',
    ]);
    $validator->validate_stakeholder_type();

    expectEnforcementToFail(
        $validator->errors,
        'PIS002',
        'ENF131',
        PISEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on stakeholder_category', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_category' => '1',
    ]);
    $validator->validate_stakeholder_category();

    expectEnforcementToPass(
        $validator->errors,
        'PIS003',
        ['ENF014', 'ENF043', 'ENF116', 'ENF131']
    );
});

it('fails rules on stakeholder_category', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_category' => null,
    ]);
    $validator->validate_stakeholder_category();

    expectEnforcementToFail(
        $validator->errors,
        'PIS003',
        'ENF014',
        PISEnforcements::ENF014_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_category' => '99',
    ]);
    $validator->validate_stakeholder_category();

    expectEnforcementToFail(
        $validator->errors,
        'PIS003',
        'ENF043',
        PISEnforcements::ENF043_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_category' => '-1',
    ]);
    $validator->validate_stakeholder_category();

    expectEnforcementToFail(
        $validator->errors,
        'PIS003',
        'ENF116',
        PISEnforcements::ENF116_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'stakeholder_category' => 'O',
    ]);
    $validator->validate_stakeholder_category();

    expectEnforcementToFail(
        $validator->errors,
        'PIS003',
        'ENF131',
        PISEnforcements::ENF131_RULE['title']
    );
});

it('passes rules on shareholder_percentage', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'shareholder_percentage' => '10.25',
        'stakeholder_category' => '1',
    ]);
    $validator->validate_shareholder_percentage();

    expectEnforcementToPass(
        $validator->errors,
        'PIS004',
        ['ENF037', 'ENF116', 'ENF133']
    );
});

it('fails rules on shareholder_percentage', function () {
    $validator = new ParticipatingInstitutionStakeholders([
        'shareholder_percentage' => '',
        'stakeholder_category' => '3',
    ]);
    $validator->validate_shareholder_percentage();

    expectEnforcementToFail(
        $validator->errors,
        'PIS004',
        'ENF037',
        PISEnforcements::ENF037_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'shareholder_percentage' => '-1',
        'stakeholder_category' => '3',
    ]);
    $validator->validate_shareholder_percentage();

    expectEnforcementToFail(
        $validator->errors,
        'PIS004',
        'ENF116',
        PISEnforcements::ENF116_RULE['title']
    );

    $validator = new ParticipatingInstitutionStakeholders([
        'shareholder_percentage' => 'O',
        'stakeholder_category' => '3',
    ]);
    $validator->validate_shareholder_percentage();

    expectEnforcementToFail(
        $validator->errors,
        'PIS004',
        'ENF133',
        PISEnforcements::ENF133_RULE['title']
    );
});
