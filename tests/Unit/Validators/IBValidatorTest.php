<?php

use App\Enforcements\IBEnforcements;

it('passes a valid branch code', function () {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_identification_code' => '001',
    ]);

    $validator->validate_branch_identification_code('IB002');

    expect($validator->errors())->toBeEmpty();
});

// Checks if validator enforces ENF014
it('requires branch code', function ($branchCode) {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_identification_code' => $branchCode,
    ]);

    $validator->validate_branch_identification_code('IB002');

    expectEnforcementToFail(
        $validator->errors,
        'IB002',
        'ENF014',
        IBEnforcements::ENF014_RULE['title']
    );
})->with(['', null]);

// Checks if validator enforces ENF068
it('ensures branch code is not negative', function () {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_identification_code' => '-10',
    ]);
    $validator->validate_branch_identification_code('IB002');

    expectEnforcementToFail(
        $validator->errors,
        'IB002',
        'ENF116',
        IBEnforcements::ENF116_RULE['title']
    );
});

it('ensures branch code exists for PI', function () {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_identification_code' => '999999',
    ]);
    $validator->validate_branch_identification_code('IB002');

    expectEnforcementToFail(
        $validator->errors,
        'IB002',
        'ENF121',
        IBEnforcements::ENF121_RULE['title']
    );
});

it('ensures branch code is a number', function () {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_identification_code' => 'ABC',
    ]);

    $validator->validate_branch_identification_code('IB002');

    expectEnforcementToFail(
        $validator->errors,
        'IB002',
        'ENF131',
        IBEnforcements::ENF131_RULE['title']
    );
});

it('fails an empty branch name', function ($branchName) {
    $validator = new \App\Validators\InstitutionBranch(['branch_name' => $branchName]);
    $validator->validate_branch_name();

    expectEnforcementToFail(
        $validator->errors,
        'IB003',
        'ENF014',
        IBEnforcements::ENF014_RULE['title']
    );
})->with(['', null]);

it('fails a non alphanumeric branch name', function () {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_name' => 'NTIND~',
    ]);
    $validator->validate_branch_name();

    expectEnforcementToFail(
        $validator->errors,
        'IB003',
        'ENF132',
        IBEnforcements::ENF132_RULE['title']
    );
});

it('fails a long branch name', function () {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_name' => \Illuminate\Support\Str::random(101),
    ]);
    $validator->validate_branch_name();

    expectEnforcementToFail(
        $validator->errors,
        'IB003',
        'ENF149',
        IBEnforcements::ENF149_RULE['title']
    );
});

it('fails an empty branch type', function ($branchType) {
    $validator = new \App\Validators\InstitutionBranch([
        'pi_identification_code' => 'CB005',
        'branch_type' => $branchType,
    ]);
    $validator->validate_branch_type();

    expectEnforcementToFail(
        $validator->errors,
        'IB004',
        'ENF014',
        IBEnforcements::ENF014_RULE['title']
    );
})->with(['', null]);

it('passes for a known branch type', function ($branchType) {
    $validator = new \App\Validators\InstitutionBranch(['branch_type' => $branchType]);
    $validator->validate_branch_type();

    expect($validator->errors())
        ->toBeEmpty();
})->with(['A', 'B']);

it('fails for an unknown branch type', function () {
    $validator = new \App\Validators\InstitutionBranch(['branch_type' => 'C']);
    $validator->validate_branch_type();

    expectEnforcementToFail(
        $validator->errors,
        'IB004',
        'ENF070',
        IBEnforcements::ENF070_RULE['title']
    );
});

it('fails for a non alphanumeric branch type', function () {
    $validator = new \App\Validators\InstitutionBranch(['branch_type' => '~']);
    $validator->validate_branch_type();

    expectEnforcementToFail(
        $validator->errors,
        'IB004',
        'ENF130',
        IBEnforcements::ENF130_RULE['title']
    );
});

it('fails for an empty date opened', function ($dateOpened) {
    $validator = new \App\Validators\InstitutionBranch(['date_opened' => $dateOpened]);
    $validator->validate_date_opened();

    expectEnforcementToFail(
        $validator->errors,
        'IB005',
        'ENF014',
        IBEnforcements::ENF014_RULE['title']
    );
})->with(['', null]);

it('fails for an invalid date opened', function ($dateOpened) {
    $validator = new \App\Validators\InstitutionBranch(['date_opened' => $dateOpened]);
    $validator->validate_date_opened();

    expectEnforcementToFail(
        $validator->errors,
        'IB005',
        'ENF007',
        IBEnforcements::ENF007_RULE['title']
    );
})->with(['20231032', '20231310', '00001031', '00000000', '2O231031', '2023I03I']);

it('fails for a non numeric date opened', function ($dateOpened) {
    $validator = new \App\Validators\InstitutionBranch(['date_opened' => $dateOpened]);
    $validator->validate_date_opened();

    expectEnforcementToFail(
        $validator->errors,
        'IB005',
        'ENF131',
        IBEnforcements::ENF131_RULE['title']
    );
})->with(['2O231031', '2023I03I']);

it('fails for long date opened', function () {
    $validator = new \App\Validators\InstitutionBranch(['date_opened' => '202310311']);
    $validator->validate_date_opened();

    expectEnforcementToFail(
        $validator->errors,
        'IB005',
        'ENF140',
        IBEnforcements::ENF140_RULE['title']
    );
});
