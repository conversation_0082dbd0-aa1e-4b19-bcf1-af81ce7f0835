<?php

use App\Enforcements\SCIEnforcements;
use App\Validators\SecondaryContactInformation;

it('fails rules on unit number', function () {
    $validator = new SecondaryContactInformation(['sci_unit_number' => 'A123~'], 'PI');
    $validator->validate_unit_number('SCI001');

    expectEnforcementToFail(
        $validator->errors,
        'SCI001',
        'ENF132',
        SCIEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on building_name', function () {
    $validator = new SecondaryContactInformation(['sci_unit_name' => 'A123~'], 'PI');
    $validator->validate_building_name('SCI002');

    expectEnforcementToFail(
        $validator->errors,
        'SCI002',
        'ENF132',
        SCIEnforcements::ENF132_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_unit_name' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_building_name('SCI002');

    expectEnforcementToFail(
        $validator->errors,
        'SCI002',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on floor number', function () {
    $validator = new SecondaryContactInformation(['sci_floor_number' => 'A123~'], 'PI');
    $validator->validate_floor_number('SCI003');

    expectEnforcementToFail(
        $validator->errors,
        'SCI003',
        'ENF132',
        SCIEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on plot_or_street_number', function () {
    $validator = new SecondaryContactInformation([
        'sci_plot_or_street_number' => 'A123~',
    ], 'PI');
    $validator->validate_plot_or_street_number('SCI004');

    expectEnforcementToFail(
        $validator->errors,
        'SCI004',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_plot_or_street_number' => fake()->sentence(5),
    ], 'PI');
    $validator->validate_plot_or_street_number('SCI004');

    expectEnforcementToFail(
        $validator->errors,
        'SCI004',
        'ENF142',
        SCIEnforcements::ENF142_RULE['title']
    );
});

it('fails rules on lc_or_street_name', function () {
    $validator = new SecondaryContactInformation([
        'sci_lc_or_street_name' => 'A123~',
    ], 'PI');
    $validator->validate_lc_or_street_name('SCI005');

    expectEnforcementToFail(
        $validator->errors,
        'SCI005',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_lc_or_street_name' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_lc_or_street_name('SCI005');

    expectEnforcementToFail(
        $validator->errors,
        'SCI005',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on parish', function () {
    $validator = new SecondaryContactInformation([
        'sci_parish' => 'Parish~',
        'sci_country_code' => 'UG',
    ], 'PI');
    $validator->validate_parish('SCI006');

    expectEnforcementToFail(
        $validator->errors,
        'SCI006',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_parish' => fake()->sentence(25),
        'sci_country_code' => 'UG',
    ], 'CAP');
    $validator->validate_parish('SCI006');

    expectEnforcementToFail(
        $validator->errors,
        'SCI006',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on suburb', function () {
    $validator = new SecondaryContactInformation([
        'sci_suburb' => 'Suburb~',
    ], 'PI');
    $validator->validate_suburb('SCI007');

    expectEnforcementToFail(
        $validator->errors,
        'SCI007',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_suburb' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_suburb('SCI007');

    expectEnforcementToFail(
        $validator->errors,
        'SCI007',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on village', function () {
    $validator = new SecondaryContactInformation([
        'sci_village' => 'Village~',
    ], 'PI');
    $validator->validate_village('SCI008');

    expectEnforcementToFail(
        $validator->errors,
        'SCI008',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_village' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_village('SCI008');

    expectEnforcementToFail(
        $validator->errors,
        'SCI008',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on county_or_town', function () {
    $validator = new SecondaryContactInformation([
        'sci_county_or_town' => 'County~',
    ], 'PI');
    $validator->validate_county_or_town('SCI009');

    expectEnforcementToFail(
        $validator->errors,
        'SCI009',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_county_or_town' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_county_or_town('SCI009');

    expectEnforcementToFail(
        $validator->errors,
        'SCI009',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on district', function () {
    $validator = new SecondaryContactInformation([
        'sci_district' => 'District~',
    ], 'PI');
    $validator->validate_district('SCI010');

    expectEnforcementToFail(
        $validator->errors,
        'SCI010',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_district' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_district('SCI010');

    expectEnforcementToFail(
        $validator->errors,
        'SCI010',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on region', function () {
    $validator = new SecondaryContactInformation([
        'sci_region' => null,
    ], 'CAP');
    $validator->validate_region('SCI011');

    expect($validator->errors)->toBeEmpty();

    $validator = new SecondaryContactInformation([
        'sci_region' => '99',
    ], 'CAP');
    $validator->validate_region('SCI011');

    expectEnforcementToFail(
        $validator->errors,
        'SCI011',
        'ENF049',
        SCIEnforcements::ENF049_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI011',
        'ENF134',
        SCIEnforcements::ENF134_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_region' => 'O',
    ], 'CAP');
    $validator->validate_region('SCI011');

    expectEnforcementToFail(
        $validator->errors,
        'SCI011',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_region' => '-1',
    ], 'CAP');
    $validator->validate_region('SCI011');

    expectEnforcementToFail(
        $validator->errors,
        'SCI011',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on po_box_number', function () {
    $validator = new SecondaryContactInformation([
        'sci_po_box_number' => 'A123~',
    ], 'PI');
    $validator->validate_po_box_number('SCI012');

    expectEnforcementToFail(
        $validator->errors,
        'SCI012',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_po_box_number' => fake()->sentence(5),
    ], 'PI');
    $validator->validate_po_box_number('SCI012');

    expectEnforcementToFail(
        $validator->errors,
        'SCI012',
        'ENF142',
        SCIEnforcements::ENF142_RULE['title']
    );
});

it('fails rules on post_office_town', function () {
    $validator = new SecondaryContactInformation([
        'sci_post_office_town' => 'A123~',
    ], 'PI');
    $validator->validate_post_office_town('SCI013');

    expectEnforcementToFail(
        $validator->errors,
        'SCI013',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_post_office_town' => fake()->sentence(10),
    ], 'PI');
    $validator->validate_post_office_town('SCI013');

    expectEnforcementToFail(
        $validator->errors,
        'SCI013',
        'ENF144',
        SCIEnforcements::ENF144_RULE['title']
    );
});

it('fails rules on country_code', function () {
    $validator = new SecondaryContactInformation([
        'sci_country_code' => 'XXX~',
    ], 'PI');
    $validator->validate_country_code('SCI014');

    expectEnforcementToFail(
        $validator->errors,
        'SCI014',
        'ENF065',
        SCIEnforcements::ENF065_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI014',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI014',
        'ENF135',
        SCIEnforcements::ENF135_RULE['title']
    );
});

it('fails rules on period_at_address', function () {
    $validator = new SecondaryContactInformation([
        'sci_period_at_address' => '-1',
    ], 'CAP');
    $validator->validate_period_at_address('SCI015');

    expectEnforcementToFail(
        $validator->errors,
        'SCI015',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_period_at_address' => 'O',
    ], 'CAP');
    $validator->validate_period_at_address('SCI015');

    expectEnforcementToFail(
        $validator->errors,
        'SCI015',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on flag_of_ownership', function () {
    $validator = new SecondaryContactInformation([
        'sci_flag_of_ownership' => 'TT',
    ], 'CAP');
    $validator->validate_flag_of_ownership('SCI016');

    expectEnforcementToFail(
        $validator->errors,
        'SCI016',
        'ENF084',
        SCIEnforcements::ENF084_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI016',
        'ENF134',
        SCIEnforcements::ENF134_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_flag_of_ownership' => '~',
    ], 'CAP');
    $validator->validate_flag_of_ownership('SCI016');

    expectEnforcementToFail(
        $validator->errors,
        'SCI016',
        'ENF130',
        SCIEnforcements::ENF130_RULE['title']
    );
});

it('fails rules on primary_number_country_dialling_code', function () {
    $validator = new SecondaryContactInformation([
        'sci_primary_number_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_primary_number_country_dialling_code('SCI017');

    expectEnforcementToFail(
        $validator->errors,
        'SCI017',
        'ENF056',
        SCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI017',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI017',
        'ENF138',
        SCIEnforcements::ENF138_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_primary_number_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_primary_number_country_dialling_code('SCI017');

    expectEnforcementToFail(
        $validator->errors,
        'SCI017',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_primary_number_country_dialling_code' => '',
    ], 'CAP');
    $validator->validate_primary_number_country_dialling_code('SCI017');

    expectEnforcementToFail(
        $validator->errors,
        'SCI017',
        'ENF164',
        SCIEnforcements::ENF164_RULE['title']
    );
});

it('fails rules on primary_number_telephone_number', function () {
    $validator = new SecondaryContactInformation([
        'sci_primary_number_telephone_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_primary_number_telephone_number('SCI018');

    expectEnforcementToFail(
        $validator->errors,
        'SCI018',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI018',
        'ENF142',
        SCIEnforcements::ENF142_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_primary_number_telephone_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_primary_number_telephone_number('SCI018');
    expectEnforcementToFail(
        $validator->errors,
        'SCI018',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_primary_number_telephone_number' => '',
    ], 'CAP');
    $validator->validate_primary_number_telephone_number('SCI018');

    expectEnforcementToFail(
        $validator->errors,
        'SCI018',
        'ENF164',
        SCIEnforcements::ENF164_RULE['title']
    );
});

it('fails rules on other_number_country_dialling_code', function () {
    $validator = new SecondaryContactInformation([
        'sci_other_number_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_other_number_country_dialling_code('SCI019');

    expectEnforcementToFail(
        $validator->errors,
        'SCI019',
        'ENF056',
        SCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI019',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI019',
        'ENF138',
        SCIEnforcements::ENF138_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_other_number_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_other_number_country_dialling_code('SCI019');

    expectEnforcementToFail(
        $validator->errors,
        'SCI019',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on other_number_telephone_number', function () {
    $validator = new SecondaryContactInformation([
        'sci_other_number_telephone_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_other_number_telephone_number('SCI020');

    expectEnforcementToFail(
        $validator->errors,
        'SCI020',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI020',
        'ENF142',
        SCIEnforcements::ENF142_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_other_number_telephone_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_other_number_telephone_number('SCI020');
    expectEnforcementToFail(
        $validator->errors,
        'SCI020',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on mobile_number_country_dialling_code', function () {
    $validator = new SecondaryContactInformation([
        'sci_mobile_number_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_mobile_number_country_dialling_code('SCI021');

    expectEnforcementToFail(
        $validator->errors,
        'SCI021',
        'ENF056',
        SCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI021',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI021',
        'ENF138',
        SCIEnforcements::ENF138_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_mobile_number_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_mobile_number_country_dialling_code('SCI021');

    expectEnforcementToFail(
        $validator->errors,
        'SCI021',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on mobile_number_telephone_number', function () {
    $validator = new SecondaryContactInformation([
        'sci_mobile_number_telephone_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_mobile_number_telephone_number('SCI022');

    expectEnforcementToFail(
        $validator->errors,
        'SCI022',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI022',
        'ENF142',
        SCIEnforcements::ENF142_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_mobile_number_telephone_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_mobile_number_telephone_number('SCI022');
    expectEnforcementToFail(
        $validator->errors,
        'SCI022',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on facsimile_country_dialling_code', function () {
    $validator = new SecondaryContactInformation([
        'sci_facsimile_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_facsimile_country_dialling_code('SCI023');

    expectEnforcementToFail(
        $validator->errors,
        'SCI023',
        'ENF056',
        SCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI023',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI023',
        'ENF138',
        SCIEnforcements::ENF138_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_facsimile_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_facsimile_country_dialling_code('SCI023');

    expectEnforcementToFail(
        $validator->errors,
        'SCI023',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on facsimile_number', function () {
    $validator = new SecondaryContactInformation([
        'sci_facsimile_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_facsimile_number('SCI024');

    expectEnforcementToFail(
        $validator->errors,
        'SCI024',
        'ENF116',
        SCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'SCI024',
        'ENF142',
        SCIEnforcements::ENF142_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_facsimile_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_facsimile_number('SCI024');
    expectEnforcementToFail(
        $validator->errors,
        'SCI024',
        'ENF131',
        SCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on email_address', function () {
    $validator = new SecondaryContactInformation(['sci_email_address' => 'A123~'], 'PI');
    $validator->validate_email_address('SCI025');

    expectEnforcementToFail(
        $validator->errors,
        'SCI025',
        'ENF132',
        SCIEnforcements::ENF132_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_email_address' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_email_address('SCI025');

    expectEnforcementToFail(
        $validator->errors,
        'SCI025',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on web_site', function () {
    $validator = new SecondaryContactInformation(['sci_web_site' => 'A123~'], 'PI');
    $validator->validate_web_site('SCI026');

    expectEnforcementToFail(
        $validator->errors,
        'SCI026',
        'ENF132',
        SCIEnforcements::ENF132_RULE['title']
    );

    $validator = new SecondaryContactInformation([
        'sci_web_site' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_web_site('SCI026');

    expectEnforcementToFail(
        $validator->errors,
        'SCI026',
        'ENF148',
        SCIEnforcements::ENF148_RULE['title']
    );
});
