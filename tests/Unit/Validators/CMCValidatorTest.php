<?php

use App\Enforcements\CMCEnforcements;
use App\Validators\CollateralMaterialCollateral;

it('passes rules for pi_identification_code', function () {
    $validator = new CollateralMaterialCollateral([
        'pi_identification_code' => 'CB005',
    ]);

    $validator->validate_pi_identification_code();

    expectEnforcementToPass($validator->errors, 'CMC001', ['ENF014', 'ENF068', 'ENF130']);
});

it('fails rules on pi_identification_code', function () {
    $validator = new CollateralMaterialCollateral([
        'pi_identification_code' => null,
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC001',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'pi_identification_code' => 'CB999',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC001',
        'ENF068',
        CMCEnforcements::ENF068_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'pi_identification_code' => 'CB9909~',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC001',
        'ENF130',
        CMCEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on branch_identification_code', function () {
    $validator = new CollateralMaterialCollateral([
        'branch_identification_code' => '001',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToPass(
        $validator->errors,
        'CMC002',
        ['ENF014', 'ENF116', 'ENF121', 'ENF131']
    );
});

it('fails rules on branch_identification_code', function () {
    $validator = new CollateralMaterialCollateral([
        'branch_identification_code' => null,
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC002',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'branch_identification_code' => '-1',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC002',
        'ENF116',
        CMCEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'branch_identification_code' => '999',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC002',
        'ENF121',
        CMCEnforcements::ENF121_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'branch_identification_code' => 'O',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CMC002',
        'ENF131',
        CMCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on borrowers_client_number', function () {
    $validator = new CollateralMaterialCollateral([
        'borrowers_client_number' => null,
    ]);
    $validator->validate_borrower_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CMC003',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'borrowers_client_number' => 'SAC0192933~',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_borrower_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CMC003',
        'ENF132',
        CMCEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on borrower_account_reference', function () {
    $validator = new CollateralMaterialCollateral([
        'borrower_account_reference' => null,
    ]);
    $validator->validate_borrower_account_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CMC004',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'borrower_account_reference' => 'A192393~',
    ]);
    $validator->validate_borrower_account_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CMC004',
        'ENF132',
        CMCEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on borrower_classification', function () {
    $validator = new CollateralMaterialCollateral([
        'borrower_classification' => null,
    ]);
    $validator->validate_borrower_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC005',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'borrower_classification' => '99',
    ]);
    $validator->validate_borrower_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC005',
        'ENF071',
        CMCEnforcements::ENF071_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CMC005',
        'ENF134',
        CMCEnforcements::ENF134_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'borrower_classification' => '-1',
    ]);
    $validator->validate_borrower_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC005',
        'ENF116',
        CMCEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'borrower_classification' => 'O',
    ]);
    $validator->validate_borrower_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC005',
        'ENF131',
        CMCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on collateral_type_identification', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_type_identification' => null,
    ]);
    $validator->validate_collateral_type_identification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC006',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_type_identification' => '99',
    ]);
    $validator->validate_collateral_type_identification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC006',
        'ENF047',
        CMCEnforcements::ENF047_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_type_identification' => '-1',
    ]);
    $validator->validate_collateral_type_identification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC006',
        'ENF116',
        CMCEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_type_identification' => 'O',
    ]);
    $validator->validate_collateral_type_identification();

    expectEnforcementToFail(
        $validator->errors,
        'CMC006',
        'ENF131',
        CMCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on collateral_reference_number', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_reference_number' => null,
    ]);
    $validator->validate_collateral_reference_number();

    expectEnforcementToFail(
        $validator->errors,
        'CMC007',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_reference_number' => 'O~',
    ]);
    $validator->validate_collateral_reference_number();

    expectEnforcementToFail(
        $validator->errors,
        'CMC007',
        'ENF132',
        CMCEnforcements::ENF132_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_reference_number' => \Illuminate\Support\Str::random(21),
    ]);
    $validator->validate_collateral_reference_number();

    expectEnforcementToFail(
        $validator->errors,
        'CMC007',
        'ENF144',
        CMCEnforcements::ENF144_RULE['title']
    );
});

it('fails rules on collateral_description', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_description' => null,
    ]);
    $validator->validate_collateral_description();

    expectEnforcementToFail(
        $validator->errors,
        'CMC008',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_description' => 'G424232534~',
    ]);
    $validator->validate_collateral_description();

    expectEnforcementToFail(
        $validator->errors,
        'CMC008',
        'ENF132',
        CMCEnforcements::ENF132_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_description' => fake()->text(1230),
    ]);
    $validator->validate_collateral_description();

    expectEnforcementToFail(
        $validator->errors,
        'CMC008',
        'ENF150',
        CMCEnforcements::ENF150_RULE['title']
    );
});

it('fails rules on collateral_currency', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_currency' => null,
    ]);
    $validator->validate_collateral_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CMC009',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_currency' => 'XXX~',
    ]);
    $validator->validate_collateral_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CMC009',
        'ENF130',
        CMCEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CMC009',
        'ENF136',
        CMCEnforcements::ENF136_RULE['title']
    );
});

it('fails rules on collateral_open_market_value', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_open_market_value' => null,
    ]);
    $validator->validate_collateral_open_market_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_open_market_value' => '-100000',
    ]);
    $validator->validate_collateral_open_market_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF116',
        CMCEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_open_market_value' => '1OOOOO',
    ]);
    $validator->validate_collateral_open_market_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF133',
        CMCEnforcements::ENF133_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_open_market_value' => '10000100000000010100101203030',
    ]);
    $validator->validate_collateral_open_market_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF146',
        CMCEnforcements::ENF146_RULE['title']
    );
});

it('fails rules on collateral_forced_sale_value', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_forced_sale_value' => null,
    ]);
    $validator->validate_collateral_forced_sale_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_forced_sale_value' => '-100000',
    ]);
    $validator->validate_collateral_forced_sale_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF116',
        CMCEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_forced_sale_value' => '1OOOOO',
    ]);
    $validator->validate_collateral_forced_sale_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF133',
        CMCEnforcements::ENF133_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_forced_sale_value' => '10000100000000010100101203030',
    ]);
    $validator->validate_collateral_forced_sale_value();

    expectEnforcementToFail(
        $validator->errors,
        'CMC010',
        'ENF146',
        CMCEnforcements::ENF146_RULE['title']
    );
});

it('fails rules on collateral_valuation_expiry_date', function () {
    $validator = new CollateralMaterialCollateral([
        'collateral_valuation_expiry_date' => null,
    ]);
    $validator->validate_collateral_valuation_expiry_date();

    expectEnforcementToFail(
        $validator->errors,
        'CMC011',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'collateral_valuation_expiry_date' => '20231031O',
    ]);
    $validator->validate_collateral_valuation_expiry_date();

    expectEnforcementToFail(
        $validator->errors,
        'CMC011',
        'ENF007',
        CMCEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CMC011',
        'ENF131',
        CMCEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CMC011',
        'ENF140',
        CMCEnforcements::ENF140_RULE['title']
    );
});

it('fails rules on instrument_of_claim', function () {
    $validator = new CollateralMaterialCollateral([
        'instrument_of_claim' => null,
    ]);
    $validator->validate_instrument_of_claim();

    expectEnforcementToFail(
        $validator->errors,
        'CMC012',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'instrument_of_claim' => 'Instrument~',
    ]);
    $validator->validate_instrument_of_claim();

    expectEnforcementToFail(
        $validator->errors,
        'CMC012',
        'ENF130',
        CMCEnforcements::ENF130_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'instrument_of_claim' => fake()->sentence(15),
    ]);
    $validator->validate_instrument_of_claim();

    expectEnforcementToFail(
        $validator->errors,
        'CMC012',
        'ENF147',
        CMCEnforcements::ENF147_RULE['title']
    );
});

it('fails rules on valuation_date', function () {
    $validator = new CollateralMaterialCollateral([
        'valuation_date' => null,
    ]);
    $validator->validate_valuation_date();

    expectEnforcementToFail(
        $validator->errors,
        'CMC014',
        'ENF014',
        CMCEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'valuation_date' => '20231101',
        'submission_date' => '20231031',
    ]);
    $validator->validate_valuation_date();

    expectEnforcementToFail(
        $validator->errors,
        'CMC014',
        'ENF063',
        CMCEnforcements::ENF063_RULE['title']
    );

    $validator = new CollateralMaterialCollateral([
        'valuation_date' => '20231031O',
        'submission_date' => '20231031',
    ]);
    $validator->validate_valuation_date();

    expectEnforcementToFail(
        $validator->errors,
        'CMC014',
        'ENF007',
        CMCEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CMC014',
        'ENF131',
        CMCEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CMC014',
        'ENF140',
        CMCEnforcements::ENF140_RULE['title']
    );
});
