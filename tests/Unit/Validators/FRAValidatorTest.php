<?php

use App\Enforcements\FRAEnforcements;
use App\Validators\FinancialMalpracticeData;

it('passes rules for pi_identification_code', function () {
    $validator = new FinancialMalpracticeData([
        'pi_identification_code' => 'CB005',
    ]);

    $validator->validate_pi_identification_code();

    expect($validator->errors())->toBeEmpty();
});

it('fails rules on pi_identification_code', function () {
    $validator = new FinancialMalpracticeData([
        'pi_identification_code' => null,
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD001',
        'ENF014',
        FRAEnforcements::ENF014_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'pi_identification_code' => 'CB999',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD001',
        'ENF068',
        FRAEnforcements::ENF068_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'pi_identification_code' => 'CB9909~',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD001',
        'ENF130',
        FRAEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on branch_identification_code', function () {
    $validator = new FinancialMalpracticeData([
        'branch_identification_code' => '001',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expect($validator->errors)->toBeEmpty();
});

it('fails rules on branch_identification_code', function () {
    $validator = new FinancialMalpracticeData([
        'branch_identification_code' => null,
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD002',
        'ENF014',
        FRAEnforcements::ENF014_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'branch_identification_code' => '-1',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD002',
        'ENF116',
        FRAEnforcements::ENF116_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'branch_identification_code' => '999',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD002',
        'ENF121',
        FRAEnforcements::ENF121_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'branch_identification_code' => 'O',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD002',
        'ENF131',
        FRAEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on client_number', function () {
    $validator = new FinancialMalpracticeData([
        'client_number' => null,
    ]);
    $validator->validate_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'FMD003',
        'ENF014',
        FRAEnforcements::ENF014_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'client_number' => 'SAC0192933~',
    ]);
    $validator->validate_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'FMD003',
        'ENF132',
        FRAEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on consumer_classification', function () {
    $validator = new FinancialMalpracticeData([
        'consumer_classification' => null,
        'client_number' => 'SAC019293399',
    ]);
    $validator->validate_consumer_classification();

    expectEnforcementToFail(
        $validator->errors,
        'FMD004',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'consumer_classification' => '99',
    ]);
    $validator->validate_consumer_classification();

    expectEnforcementToFail(
        $validator->errors,
        'FMD004',
        'ENF071',
        FRAEnforcements::ENF071_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD004',
        'ENF134',
        FRAEnforcements::ENF134_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'consumer_classification' => '-1',
    ]);
    $validator->validate_consumer_classification();

    expectEnforcementToFail(
        $validator->errors,
        'FMD004',
        'ENF116',
        FRAEnforcements::ENF116_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'consumer_classification' => 'O',
    ]);
    $validator->validate_consumer_classification();

    expectEnforcementToFail(
        $validator->errors,
        'FMD004',
        'ENF131',
        FRAEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on category_code', function () {
    $validator = new FinancialMalpracticeData([
        'category_code' => null,
        'client_number' => 'C00023232324',
    ]);
    $validator->validate_category_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD005',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'category_code' => '99',
    ]);
    $validator->validate_category_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD005',
        'ENF051',
        FRAEnforcements::ENF051_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'category_code' => '-1',
    ]);
    $validator->validate_category_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD005',
        'ENF116',
        FRAEnforcements::ENF116_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'category_code' => 'O',
    ]);
    $validator->validate_category_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD005',
        'ENF131',
        FRAEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on sub_category_code', function () {
    $validator = new FinancialMalpracticeData([
        'sub_category_code' => null,
        'client_number' => 'C00023232324',
    ]);
    $validator->validate_subcategory_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD006',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'sub_category_code' => '99',
        'category_code' => '2',
    ]);
    $validator->validate_subcategory_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD006',
        'ENF052',
        FRAEnforcements::ENF052_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'sub_category_code' => '-1',
        'category_code' => '2',
    ]);
    $validator->validate_subcategory_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD006',
        'ENF116',
        FRAEnforcements::ENF116_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'sub_category_code' => 'O',
        'category_code' => '2',
    ]);
    $validator->validate_subcategory_code();

    expectEnforcementToFail(
        $validator->errors,
        'FMD006',
        'ENF131',
        FRAEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on incident_date', function () {
    $validator = new FinancialMalpracticeData([
        'incident_date' => null,
    ]);
    $validator->validate_incident_date();

    expect($validator->errors)->toBeEmpty();

    $validator = new FinancialMalpracticeData([
        'incident_date' => '20231031O',
        'submission_date' => '20231031',
    ]);
    $validator->validate_incident_date();

    expectEnforcementToFail(
        $validator->errors,
        'FMD007',
        'ENF007',
        FRAEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD007',
        'ENF131',
        FRAEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD007',
        'ENF140',
        FRAEnforcements::ENF140_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'incident_date' => '20231101',
        'submission_date' => '20231031',
    ]);
    $validator->validate_incident_date();

    expectEnforcementToFail(
        $validator->errors,
        'FMD007',
        'ENF013',
        FRAEnforcements::ENF013_RULE['title']
    );
});

it('fails rules on loss_amount', function () {
    $validator = new FinancialMalpracticeData([
        'loss_amount' => null,
        'client_number' => 'A10293399444',
    ]);
    $validator->validate_loss_amount();

    expectEnforcementToFail(
        $validator->errors,
        'FMD008',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'loss_amount' => '-20000',
    ]);
    $validator->validate_loss_amount();

    expectEnforcementToFail(
        $validator->errors,
        'FMD008',
        'ENF116',
        FRAEnforcements::ENF116_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'loss_amount' => '2000O',
    ]);
    $validator->validate_loss_amount();

    expectEnforcementToFail(
        $validator->errors,
        'FMD008',
        'ENF133',
        FRAEnforcements::ENF133_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'loss_amount' => '2001000000000003000000000300000040000',
    ]);
    $validator->validate_loss_amount();

    expectEnforcementToFail(
        $validator->errors,
        'FMD008',
        'ENF146',
        FRAEnforcements::ENF146_RULE['title']
    );
});

it('fails rules on currency_type', function () {
    $validator = new FinancialMalpracticeData([
        'currency_type' => null,
        'client_number' => 'A0938484894',
    ]);
    $validator->validate_currency_type();

    expectEnforcementToFail(
        $validator->errors,
        'FMD009',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'currency_type' => 'XXX~',
    ]);
    $validator->validate_currency_type();

    expectEnforcementToFail(
        $validator->errors,
        'FMD009',
        'ENF050',
        FRAEnforcements::ENF050_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD009',
        'ENF130',
        FRAEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD009',
        'ENF136',
        FRAEnforcements::ENF136_RULE['title']
    );
});

it('fails rules on incident_details', function () {
    $validator = new FinancialMalpracticeData([
        'incident_details' => null,
        'client_number' => 'A0938484894',
    ]);
    $validator->validate_incident_details();

    expectEnforcementToFail(
        $validator->errors,
        'FMD010',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'incident_details' => 'Some details ~',
    ]);
    $validator->validate_incident_details();

    expectEnforcementToFail(
        $validator->errors,
        'FMD010',
        'ENF132',
        FRAEnforcements::ENF132_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'incident_details' => fake()->text(1300),
    ]);
    $validator->validate_incident_details();

    expectEnforcementToFail(
        $validator->errors,
        'FMD010',
        'ENF150',
        FRAEnforcements::ENF150_RULE['title']
    );
});

it('fails rules on forensic_information_available', function () {
    $validator = new FinancialMalpracticeData([
        'forensic_information_available' => null,
        'client_number' => 'A0938484894',
    ]);
    $validator->validate_forensic_information_available();

    expectEnforcementToFail(
        $validator->errors,
        'FMD011',
        'ENF151',
        FRAEnforcements::ENF151_RULE['title']
    );

    $validator = new FinancialMalpracticeData([
        'forensic_information_available' => 'A~',
        'client_number' => 'A0938484894',
    ]);
    $validator->validate_forensic_information_available();

    expectEnforcementToFail(
        $validator->errors,
        'FMD011',
        'ENF083',
        FRAEnforcements::ENF083_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD011',
        'ENF130',
        FRAEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'FMD011',
        'ENF134',
        FRAEnforcements::ENF134_RULE['title']
    );
});
