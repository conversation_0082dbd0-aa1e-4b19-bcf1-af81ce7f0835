<?php

use App\Enforcements\CAPEnforcements;
use App\Validators\CreditApplication;

it('passes rules for pi_identification_code', function () {
    $validator = new CreditApplication([
        'pi_identification_code' => 'CB005',
    ]);

    $validator->validate_pi_identification_code();

    expectEnforcementToPass($validator->errors, 'CAP001', ['ENF014', 'ENF068', 'ENF130']);
});

it('fails rules on pi_identification_code', function () {
    $validator = new CreditApplication([
        'pi_identification_code' => null,
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP001',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'pi_identification_code' => 'CB999',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP001',
        'ENF068',
        CAPEnforcements::ENF068_RULE['title']
    );

    $validator = new CreditApplication([
        'pi_identification_code' => 'CB9909~',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP001',
        'ENF130',
        CAPEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on branch_identification_code', function () {
    $validator = new CreditApplication([
        'branch_identification_code' => '001',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToPass(
        $validator->errors,
        'CAP002',
        ['ENF014', 'ENF116', 'ENF121', 'ENF131']
    );
});

it('fails rules on branch_identification_code', function () {
    $validator = new CreditApplication([
        'branch_identification_code' => null,
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP002',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'branch_identification_code' => '-1',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP002',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditApplication([
        'branch_identification_code' => '999',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP002',
        'ENF121',
        CAPEnforcements::ENF121_RULE['title']
    );

    $validator = new CreditApplication([
        'branch_identification_code' => 'O',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CAP002',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on client_number', function () {
    $validator = new CreditApplication([
        'client_number' => 'SAC0192933~',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CAP003',
        'ENF132',
        CAPEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on credit_application_reference', function () {
    $validator = new CreditApplication([
        'credit_application_reference' => null,
    ]);
    $validator->validate_credit_application_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CAP004',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_reference' => 'A192393~',
    ]);
    $validator->validate_credit_application_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CAP004',
        'ENF132',
        CAPEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on applicant_classification', function () {
    $validator = new CreditApplication([
        'applicant_classification' => null,
    ]);
    $validator->validate_applicant_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CAP005',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'applicant_classification' => '99',
    ]);
    $validator->validate_applicant_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CAP005',
        'ENF071',
        CAPEnforcements::ENF071_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP005',
        'ENF134',
        CAPEnforcements::ENF134_RULE['title']
    );

    $validator = new CreditApplication([
        'applicant_classification' => '-1',
    ]);
    $validator->validate_applicant_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CAP005',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditApplication([
        'applicant_classification' => 'O',
    ]);
    $validator->validate_applicant_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CAP005',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on credit_application_date', function () {
    $validator = new CreditApplication([
        'credit_application_date' => null,
    ]);
    $validator->validate_credit_application_date();

    expectEnforcementToFail(
        $validator->errors,
        'CAP006',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_date' => '202310310',
        'submission_date' => '20231031',
    ]);
    $validator->validate_credit_application_date();

    expectEnforcementToFail(
        $validator->errors,
        'CAP006',
        'ENF005',
        CAPEnforcements::ENF005_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP006',
        'ENF007',
        CAPEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP006',
        'ENF140',
        CAPEnforcements::ENF140_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_date' => '20231O31',
        'submission_date' => '20231031',
    ]);
    $validator->validate_credit_application_date();

    expectEnforcementToFail(
        $validator->errors,
        'CAP006',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on amount', function () {
    $validator = new CreditApplication([
        'amount' => null,
    ]);
    $validator->validate_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CAP007',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'amount' => '-10000',
    ]);
    $validator->validate_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CAP007',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditApplication([
        'amount' => '1000O',
    ]);
    $validator->validate_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CAP007',
        'ENF133',
        CAPEnforcements::ENF133_RULE['title']
    );

    $validator = new CreditApplication([
        'amount' => '10000000000000200000300002000',
    ]);
    $validator->validate_amount();

    expectEnforcementToFail(
        $validator->errors,
        'CAP007',
        'ENF146',
        CAPEnforcements::ENF146_RULE['title']
    );
});

it('fails rules on currency', function () {
    $validator = new CreditApplication([
        'currency' => null,
    ]);
    $validator->validate_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CAP008',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'currency' => 'XXX~',
    ]);
    $validator->validate_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CAP008',
        'ENF050',
        CAPEnforcements::ENF050_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP008',
        'ENF130',
        CAPEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP008',
        'ENF136',
        CAPEnforcements::ENF136_RULE['title']
    );
});

it('fails rules on credit_account_or_loan_product_type', function () {
    $validator = new CreditApplication([
        'credit_account_or_loan_product_type' => null,
    ]);
    $validator->validate_credit_account_or_loan_product_type();

    expectEnforcementToFail(
        $validator->errors,
        'CAP009',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_account_or_loan_product_type' => '99O',
    ]);
    $validator->validate_credit_account_or_loan_product_type();

    expectEnforcementToFail(
        $validator->errors,
        'CAP009',
        'ENF061',
        CAPEnforcements::ENF061_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP009',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_account_or_loan_product_type' => '-2',
    ]);
    $validator->validate_credit_account_or_loan_product_type();

    expectEnforcementToFail(
        $validator->errors,
        'CAP009',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on credit_application_status', function () {
    $validator = new CreditApplication([
        'credit_application_status' => null,
    ]);
    $validator->validate_credit_application_status();

    expectEnforcementToFail(
        $validator->errors,
        'CAP010',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_status' => '99O',
    ]);
    $validator->validate_credit_application_status();

    expectEnforcementToFail(
        $validator->errors,
        'CAP010',
        'ENF041',
        CAPEnforcements::ENF041_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP010',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP010',
        'ENF134',
        CAPEnforcements::ENF134_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_status' => '-5',
    ]);
    $validator->validate_credit_application_status();

    expectEnforcementToFail(
        $validator->errors,
        'CAP010',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on last_status_change_date', function () {
    $validator = new CreditApplication([
        'last_status_change_date' => null,
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToFail(
        $validator->errors,
        'CAP011',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'last_status_change_date' => '202310311',
        'submission_date' => '20231031',
    ]);
    $validator->validate_last_status_change_date();

    expectEnforcementToFail(
        $validator->errors,
        'CAP011',
        'ENF007',
        CAPEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP011',
        'ENF063',
        CAPEnforcements::ENF063_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP011',
        'ENF140',
        CAPEnforcements::ENF140_RULE['title']
    );
});

it('fails rules on credit_application_duration', function () {
    $validator = new CreditApplication([
        'credit_application_duration' => null,
        'credit_application_status' => '0',
    ]);
    $validator->validate_credit_application_duration();

    expectEnforcementToFail(
        $validator->errors,
        'CAP012',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP012',
        'ENF179',
        CAPEnforcements::ENF179_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_duration' => '-10',
        'credit_application_status' => '0',
    ]);
    $validator->validate_credit_application_duration();

    expectEnforcementToFail(
        $validator->errors,
        'CAP012',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditApplication([
        'credit_application_duration' => '1293830O',
        'credit_application_status' => '0',
    ]);
    $validator->validate_credit_application_duration();

    expectEnforcementToFail(
        $validator->errors,
        'CAP012',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP012',
        'ENF138',
        CAPEnforcements::ENF138_RULE['title']
    );
});

it('fails rules on rejection_reason', function () {
    $validator = new CreditApplication([
        'rejection_reason' => null,
        'credit_application_status' => '6',
    ]);
    $validator->validate_rejection_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CAP013',
        'ENF064',
        CAPEnforcements::ENF064_RULE['title']
    );

    $validator = new CreditApplication([
        'rejection_reason' => '99O',
    ]);
    $validator->validate_rejection_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CAP013',
        'ENF042',
        CAPEnforcements::ENF042_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP013',
        'ENF131',
        CAPEnforcements::ENF131_RULE['title']
    );

    $validator = new CreditApplication([
        'rejection_reason' => '-5',
    ]);
    $validator->validate_rejection_reason();

    expectEnforcementToFail(
        $validator->errors,
        'CAP013',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on client_consent_flag', function () {
    $validator = new CreditApplication([
        'client_consent_flag' => null,
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CAP014',
        'ENF014',
        CAPEnforcements::ENF014_RULE['title']
    );

    $validator = new CreditApplication([
        'client_consent_flag' => 'O',
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CAP014',
        'ENF078',
        CAPEnforcements::ENF078_RULE['title']
    );

    $validator = new CreditApplication([
        'client_consent_flag' => 'Y~',
    ]);
    $validator->validate_client_consent_flag();

    expectEnforcementToFail(
        $validator->errors,
        'CAP014',
        'ENF130',
        CAPEnforcements::ENF130_RULE['title']
    );
});

it('fails rules on group_identification_joint_account_number', function () {
    $validator = new CreditApplication([
        'group_identification_joint_account_number' => 'G424232534~',
    ]);
    $validator->validate_group_identification_joint_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CAP015',
        'ENF130',
        CAPEnforcements::ENF130_RULE['title']
    );

    $validator = new CreditApplication([
        'group_identification_joint_account_number' => \Illuminate\Support\Str::random(31),
    ]);
    $validator->validate_group_identification_joint_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CAP015',
        'ENF147',
        CAPEnforcements::ENF147_RULE['title']
    );
});

it('fails rules on amount_approved', function () {
    $validator = new CreditApplication([
        'amount_approved' => null,
        'credit_application_status' => '0',
    ]);
    $validator->validate_amount_approved();

    expectEnforcementToFail(
        $validator->errors,
        'CAP016',
        'ENF189',
        CAPEnforcements::ENF189_RULE['title']
    );

    $validator = new CreditApplication([
        'amount_approved' => '-8383838',
    ]);
    $validator->validate_amount_approved();

    expectEnforcementToFail(
        $validator->errors,
        'CAP016',
        'ENF116',
        CAPEnforcements::ENF116_RULE['title']
    );

    $validator = new CreditApplication([
        'amount_approved' => '-838383O',
    ]);
    $validator->validate_amount_approved();

    expectEnforcementToFail(
        $validator->errors,
        'CAP016',
        'ENF133',
        CAPEnforcements::ENF133_RULE['title']
    );

    $validator = new CreditApplication([
        'amount_approved' => '83838300000000000101239201100001',
    ]);
    $validator->validate_amount_approved();

    expectEnforcementToFail(
        $validator->errors,
        'CAP016',
        'ENF146',
        CAPEnforcements::ENF146_RULE['title']
    );
});

it('fails rules on approved_currency', function () {
    $validator = new CreditApplication([
        'approved_currency' => null,
        'credit_application_status' => '0',
    ]);
    $validator->validate_approved_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CAP017',
        'ENF189',
        CAPEnforcements::ENF189_RULE['title']
    );

    $validator = new CreditApplication([
        'approved_currency' => 'XXX~',
    ]);
    $validator->validate_approved_currency();

    expectEnforcementToFail(
        $validator->errors,
        'CAP017',
        'ENF050',
        CAPEnforcements::ENF050_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP017',
        'ENF130',
        CAPEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CAP017',
        'ENF136',
        CAPEnforcements::ENF136_RULE['title']
    );
});
