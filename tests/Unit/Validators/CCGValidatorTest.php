<?php

use App\Enforcements\CCGEnforcements;
use App\Validators\CollateralCreditGuarantor;

it('passes rules for pi_identification_code', function () {
    $validator = new CollateralCreditGuarantor([
        'pi_identification_code' => 'CB005',
    ]);

    $validator->validate_pi_identification_code();

    expectEnforcementToPass($validator->errors, 'CCG001', ['ENF014', 'ENF068', 'ENF130']);
});

it('fails rules on pi_identification_code', function () {
    $validator = new CollateralCreditGuarantor([
        'pi_identification_code' => null,
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG001',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'pi_identification_code' => 'CB999',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG001',
        'ENF068',
        CCGEnforcements::ENF068_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'pi_identification_code' => 'CB9909~',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG001',
        'ENF130',
        CCGEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on branch_identification_code', function () {
    $validator = new CollateralCreditGuarantor([
        'branch_identification_code' => '001',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToPass(
        $validator->errors,
        'CCG002',
        ['ENF014', 'ENF116', 'ENF121', 'ENF131']
    );
});

it('fails rules on branch_identification_code', function () {
    $validator = new CollateralCreditGuarantor([
        'branch_identification_code' => null,
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG002',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'branch_identification_code' => '-1',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG002',
        'ENF116',
        CCGEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'branch_identification_code' => '999',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG002',
        'ENF121',
        CCGEnforcements::ENF121_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'branch_identification_code' => 'O',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'CCG002',
        'ENF131',
        CCGEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on borrowers_client_number', function () {
    $validator = new CollateralCreditGuarantor([
        'borrowers_client_number' => null,
    ]);
    $validator->validate_borrowers_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CCG003',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'borrowers_client_number' => 'SAC0192933~',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_borrowers_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CCG003',
        'ENF132',
        CCGEnforcements::ENF132_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'borrowers_client_number' => 'SAC01929330000000000030300000000023300001',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_borrowers_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'CCG003',
        'ENF147',
        CCGEnforcements::ENF147_RULE['title']
    );
});

it('fails rules on borrower_account_reference', function () {
    $validator = new CollateralCreditGuarantor([
        'borrower_account_reference' => null,
    ]);
    $validator->validate_borrowers_account_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CCG004',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'borrower_account_reference' => 'A192393~',
    ]);
    $validator->validate_borrowers_account_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CCG004',
        'ENF132',
        CCGEnforcements::ENF132_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'borrower_account_reference' => '101930000000000000983000000000001',
    ]);
    $validator->validate_borrowers_account_reference();

    expectEnforcementToFail(
        $validator->errors,
        'CCG004',
        'ENF147',
        CCGEnforcements::ENF147_RULE['title']
    );
});

it('fails rules on guarantor_classification', function () {
    $validator = new CollateralCreditGuarantor([
        'guarantor_classification' => null,
    ]);
    $validator->validate_guarantor_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CCG005',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantor_classification' => '99',
    ]);
    $validator->validate_guarantor_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CCG005',
        'ENF071',
        CCGEnforcements::ENF071_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CCG005',
        'ENF134',
        CCGEnforcements::ENF134_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantor_classification' => '-1',
    ]);
    $validator->validate_guarantor_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CCG005',
        'ENF116',
        CCGEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantor_classification' => 'O',
    ]);
    $validator->validate_guarantor_classification();

    expectEnforcementToFail(
        $validator->errors,
        'CCG005',
        'ENF131',
        CCGEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on guarantee_type', function () {
    $validator = new CollateralCreditGuarantor([
        'guarantee_type' => null,
    ]);
    $validator->validate_guarantee_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG006',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantee_type' => '99',
    ]);
    $validator->validate_guarantee_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG006',
        'ENF081',
        CCGEnforcements::ENF081_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CCG006',
        'ENF134',
        CCGEnforcements::ENF134_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantee_type' => '-1',
    ]);
    $validator->validate_guarantee_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG006',
        'ENF116',
        CCGEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantee_type' => 'O',
    ]);
    $validator->validate_guarantee_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG006',
        'ENF131',
        CCGEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on guarantor_type', function () {
    $validator = new CollateralCreditGuarantor([
        'guarantor_type' => null,
    ]);
    $validator->validate_guarantor_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG007',
        'ENF014',
        CCGEnforcements::ENF014_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantor_type' => '99',
    ]);
    $validator->validate_guarantor_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG007',
        'ENF082',
        CCGEnforcements::ENF082_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'CCG007',
        'ENF134',
        CCGEnforcements::ENF134_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantor_type' => '-1',
    ]);
    $validator->validate_guarantor_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG007',
        'ENF116',
        CCGEnforcements::ENF116_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'guarantor_type' => 'O',
    ]);
    $validator->validate_guarantor_type();

    expectEnforcementToFail(
        $validator->errors,
        'CCG007',
        'ENF131',
        CCGEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on group_identification_joint_account_number', function () {
    $validator = new CollateralCreditGuarantor([
        'group_identification_joint_account_number' => 'G424232534~',
    ]);
    $validator->validate_group_identification_joint_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CCG008',
        'ENF130',
        CCGEnforcements::ENF130_RULE['title']
    );

    $validator = new CollateralCreditGuarantor([
        'group_identification_joint_account_number' => \Illuminate\Support\Str::random(31),
    ]);
    $validator->validate_group_identification_joint_account_number();

    expectEnforcementToFail(
        $validator->errors,
        'CCG008',
        'ENF147',
        CCGEnforcements::ENF147_RULE['title']
    );
});
