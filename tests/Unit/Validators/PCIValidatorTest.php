<?php

use App\Enforcements\PCIEnforcements;
use App\Validators\PrimaryContactInformation;

it('fails rules on unit number', function () {
    $validator = new PrimaryContactInformation(['pci_unit_number' => 'A123~'], 'PI');
    $validator->validate_unit_number('PCI001');

    expectEnforcementToFail(
        $validator->errors,
        'PCI001',
        'ENF132',
        PCIEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on building_name', function () {
    $validator = new PrimaryContactInformation(['pci_building_name' => 'A123~'], 'PI');
    $validator->validate_building_name('PCI002');

    expectEnforcementToFail(
        $validator->errors,
        'PCI002',
        'ENF132',
        PCIEnforcements::ENF132_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_building_name' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_building_name('PCI002');

    expectEnforcementToFail(
        $validator->errors,
        'PCI002',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on floor number', function () {
    $validator = new PrimaryContactInformation(['pci_floor_number' => 'A123~'], 'PI');
    $validator->validate_floor_number('PCI003');

    expectEnforcementToFail(
        $validator->errors,
        'PCI003',
        'ENF132',
        PCIEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on plot_or_street_number', function () {
    $validator = new PrimaryContactInformation([
        'pci_plot_or_street_number' => 'A123~',
    ], 'PI');
    $validator->validate_plot_or_street_number('PCI004');

    expectEnforcementToFail(
        $validator->errors,
        'PCI004',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_plot_or_street_number' => fake()->sentence(5),
    ], 'PI');
    $validator->validate_plot_or_street_number('PCI004');

    expectEnforcementToFail(
        $validator->errors,
        'PCI004',
        'ENF142',
        PCIEnforcements::ENF142_RULE['title']
    );
});

it('fails rules on lc_or_street_name', function () {
    $validator = new PrimaryContactInformation([
        'pci_lc_or_street_name' => 'A123~',
    ], 'PI');
    $validator->validate_lc_or_street_name('PCI005');

    expectEnforcementToFail(
        $validator->errors,
        'PCI005',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_lc_or_street_name' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_lc_or_street_name('PCI005');

    expectEnforcementToFail(
        $validator->errors,
        'PCI005',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on parish', function () {
    $validator = new PrimaryContactInformation([
        'pci_parish' => 'Parish~',
        'pci_country_code' => 'UG',
    ], 'PI');
    $validator->validate_parish('PCI006');

    expectEnforcementToFail(
        $validator->errors,
        'PCI006',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_parish' => fake()->sentence(25),
        'pci_country_code' => 'UG',
    ], 'CAP');
    $validator->validate_parish('PCI006');

    expectEnforcementToFail(
        $validator->errors,
        'PCI006',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_parish' => null,
        'pci_country_code' => 'UG',
    ], 'CAP');
    $validator->validate_parish('PCI006');

    expectEnforcementToFail(
        $validator->errors,
        'PCI006',
        'ENF154',
        PCIEnforcements::ENF154_RULE['title']
    );
});

it('fails rules on suburb', function () {
    $validator = new PrimaryContactInformation([
        'pci_suburb' => 'Suburb~',
    ], 'PI');
    $validator->validate_suburb('PCI007');

    expectEnforcementToFail(
        $validator->errors,
        'PCI007',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_suburb' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_suburb('PCI007');

    expectEnforcementToFail(
        $validator->errors,
        'PCI007',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on village', function () {
    $validator = new PrimaryContactInformation([
        'pci_village' => 'Village~',
    ], 'PI');
    $validator->validate_village('PCI008');

    expectEnforcementToFail(
        $validator->errors,
        'PCI008',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_village' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_village('PCI008');

    expectEnforcementToFail(
        $validator->errors,
        'PCI008',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on county_or_town', function () {
    $validator = new PrimaryContactInformation([
        'pci_county_or_town' => 'County~',
    ], 'PI');
    $validator->validate_county_or_town('PCI009');

    expectEnforcementToFail(
        $validator->errors,
        'PCI009',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_county_or_town' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_county_or_town('PCI009');

    expectEnforcementToFail(
        $validator->errors,
        'PCI009',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on district', function () {
    $validator = new PrimaryContactInformation([
        'pci_district' => 'District~',
    ], 'PI');
    $validator->validate_district('PCI010');

    expectEnforcementToFail(
        $validator->errors,
        'PCI010',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_district' => fake()->sentence(25),
    ], 'CAP');
    $validator->validate_district('PCI010');

    expectEnforcementToFail(
        $validator->errors,
        'PCI010',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on region', function () {
    $validator = new PrimaryContactInformation([
        'pci_region' => null,
    ], 'CAP');
    $validator->validate_region('PCI011');

    expect($validator->errors)->toBeEmpty();

    $validator = new PrimaryContactInformation([
        'pci_region' => '99',
    ], 'CAP');
    $validator->validate_region('PCI011');

    expectEnforcementToFail(
        $validator->errors,
        'PCI011',
        'ENF049',
        PCIEnforcements::ENF049_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI011',
        'ENF134',
        PCIEnforcements::ENF134_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_region' => 'O',
    ], 'CAP');
    $validator->validate_region('PCI011');

    expectEnforcementToFail(
        $validator->errors,
        'PCI011',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_region' => '-1',
    ], 'CAP');
    $validator->validate_region('PCI011');

    expectEnforcementToFail(
        $validator->errors,
        'PCI011',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on po_box_number', function () {
    $validator = new PrimaryContactInformation([
        'pci_po_box_number' => 'A123~',
    ], 'PI');
    $validator->validate_po_box_number('PCI012');

    expectEnforcementToFail(
        $validator->errors,
        'PCI012',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_po_box_number' => fake()->sentence(5),
    ], 'PI');
    $validator->validate_po_box_number('PCI012');

    expectEnforcementToFail(
        $validator->errors,
        'PCI012',
        'ENF142',
        PCIEnforcements::ENF142_RULE['title']
    );
});

it('fails rules on post_office_town', function () {
    $validator = new PrimaryContactInformation([
        'pci_post_office_town' => 'A123~',
    ], 'PI');
    $validator->validate_post_office_town('PCI013');

    expectEnforcementToFail(
        $validator->errors,
        'PCI013',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_post_office_town' => fake()->sentence(10),
    ], 'PI');
    $validator->validate_post_office_town('PCI013');

    expectEnforcementToFail(
        $validator->errors,
        'PCI013',
        'ENF144',
        PCIEnforcements::ENF144_RULE['title']
    );
});

it('fails rules on country_code', function () {
    $validator = new PrimaryContactInformation([
        'pci_country_code' => null,
    ], 'PI');
    $validator->validate_country_code('PCI014');

    expectEnforcementToFail(
        $validator->errors,
        'PCI014',
        'ENF014',
        PCIEnforcements::ENF014_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_country_code' => 'XXX~',
    ], 'PI');
    $validator->validate_country_code('PCI014');

    expectEnforcementToFail(
        $validator->errors,
        'PCI014',
        'ENF065',
        PCIEnforcements::ENF065_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI014',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI014',
        'ENF135',
        PCIEnforcements::ENF135_RULE['title']
    );
});

it('fails rules on period_at_address', function () {
    $validator = new PrimaryContactInformation([
        'pci_period_at_address' => '-1',
    ], 'CAP');
    $validator->validate_period_at_address('PCI015');

    expectEnforcementToFail(
        $validator->errors,
        'PCI015',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_period_at_address' => 'O',
    ], 'CAP');
    $validator->validate_period_at_address('PCI015');

    expectEnforcementToFail(
        $validator->errors,
        'PCI015',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_period_at_address' => null,
    ], 'CAP');
    $validator->validate_period_at_address('PCI015');

    expectEnforcementToFail(
        $validator->errors,
        'PCI015',
        'ENF155',
        PCIEnforcements::ENF155_RULE['title']
    );
});

it('fails rules on flag_of_ownership', function () {
    $validator = new PrimaryContactInformation([
        'pci_flag_of_ownership' => null,
    ], 'CAP');
    $validator->validate_flag_of_ownership('PCI016');

    expectEnforcementToFail(
        $validator->errors,
        'PCI016',
        'ENF014',
        PCIEnforcements::ENF014_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_flag_of_ownership' => 'TT',
    ], 'CAP');
    $validator->validate_flag_of_ownership('PCI016');

    expectEnforcementToFail(
        $validator->errors,
        'PCI016',
        'ENF084',
        PCIEnforcements::ENF084_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI016',
        'ENF134',
        PCIEnforcements::ENF134_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_flag_of_ownership' => '~',
    ], 'CAP');
    $validator->validate_flag_of_ownership('PCI016');

    expectEnforcementToFail(
        $validator->errors,
        'PCI016',
        'ENF130',
        PCIEnforcements::ENF130_RULE['title']
    );
});

it('fails rules on primary_number_country_dialling_code', function () {
    $validator = new PrimaryContactInformation([
        'pci_primary_number_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_primary_number_country_dialling_code('PCI017');

    expectEnforcementToFail(
        $validator->errors,
        'PCI017',
        'ENF056',
        PCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI017',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI017',
        'ENF138',
        PCIEnforcements::ENF138_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_primary_number_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_primary_number_country_dialling_code('PCI017');

    expectEnforcementToFail(
        $validator->errors,
        'PCI017',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_primary_number_country_dialling_code' => '',
    ], 'CAP');
    $validator->validate_primary_number_country_dialling_code('PCI017');

    expectEnforcementToFail(
        $validator->errors,
        'PCI017',
        'ENF164',
        PCIEnforcements::ENF164_RULE['title']
    );
});

it('fails rules on primary_number_telephone_number', function () {
    $validator = new PrimaryContactInformation([
        'pci_primary_number_telephone_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_primary_number_telephone_number('PCI018');

    expectEnforcementToFail(
        $validator->errors,
        'PCI018',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI018',
        'ENF142',
        PCIEnforcements::ENF142_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_primary_number_telephone_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_primary_number_telephone_number('PCI018');
    expectEnforcementToFail(
        $validator->errors,
        'PCI018',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_primary_number_telephone_number' => '',
    ], 'CAP');
    $validator->validate_primary_number_telephone_number('PCI018');

    expectEnforcementToFail(
        $validator->errors,
        'PCI018',
        'ENF164',
        PCIEnforcements::ENF164_RULE['title']
    );
});

it('fails rules on other_number_country_dialling_code', function () {
    $validator = new PrimaryContactInformation([
        'pci_other_number_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_other_number_country_dialling_code('PCI019');

    expectEnforcementToFail(
        $validator->errors,
        'PCI019',
        'ENF056',
        PCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI019',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI019',
        'ENF138',
        PCIEnforcements::ENF138_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_other_number_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_other_number_country_dialling_code('PCI019');

    expectEnforcementToFail(
        $validator->errors,
        'PCI019',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on other_number_telephone_number', function () {
    $validator = new PrimaryContactInformation([
        'pci_other_number_telephone_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_other_number_telephone_number('PCI020');

    expectEnforcementToFail(
        $validator->errors,
        'PCI020',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI020',
        'ENF142',
        PCIEnforcements::ENF142_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_other_number_telephone_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_other_number_telephone_number('PCI020');
    expectEnforcementToFail(
        $validator->errors,
        'PCI020',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on mobile_number_country_dialling_code', function () {
    $validator = new PrimaryContactInformation([
        'pci_mobile_number_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_mobile_number_country_dialling_code('PCI021');

    expectEnforcementToFail(
        $validator->errors,
        'PCI021',
        'ENF056',
        PCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI021',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI021',
        'ENF138',
        PCIEnforcements::ENF138_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_mobile_number_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_mobile_number_country_dialling_code('PCI021');

    expectEnforcementToFail(
        $validator->errors,
        'PCI021',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on mobile_number_telephone_number', function () {
    $validator = new PrimaryContactInformation([
        'pci_mobile_number_telephone_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_mobile_number_telephone_number('PCI022');

    expectEnforcementToFail(
        $validator->errors,
        'PCI022',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI022',
        'ENF142',
        PCIEnforcements::ENF142_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_mobile_number_telephone_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_mobile_number_telephone_number('PCI022');
    expectEnforcementToFail(
        $validator->errors,
        'PCI022',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on facsimile_country_dialling_code', function () {
    $validator = new PrimaryContactInformation([
        'pci_facsimile_country_dialling_code' => '99999O',
    ], 'CAP');
    $validator->validate_facsimile_country_dialling_code('PCI023');

    expectEnforcementToFail(
        $validator->errors,
        'PCI023',
        'ENF056',
        PCIEnforcements::ENF056_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI023',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI023',
        'ENF138',
        PCIEnforcements::ENF138_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_facsimile_country_dialling_code' => '-1',
    ], 'CAP');
    $validator->validate_facsimile_country_dialling_code('PCI023');

    expectEnforcementToFail(
        $validator->errors,
        'PCI023',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on facsimile_number', function () {
    $validator = new PrimaryContactInformation([
        'pci_facsimile_number' => '-7384849433000',
    ], 'CAP');
    $validator->validate_facsimile_number('PCI024');

    expectEnforcementToFail(
        $validator->errors,
        'PCI024',
        'ENF116',
        PCIEnforcements::ENF116_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'PCI024',
        'ENF142',
        PCIEnforcements::ENF142_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_facsimile_number' => '-73848494O',
    ], 'CAP');
    $validator->validate_facsimile_number('PCI024');
    expectEnforcementToFail(
        $validator->errors,
        'PCI024',
        'ENF131',
        PCIEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on email_address', function () {
    $validator = new PrimaryContactInformation(['pci_email_address' => 'A123~'], 'PI');
    $validator->validate_email_address('PCI025');

    expectEnforcementToFail(
        $validator->errors,
        'PCI025',
        'ENF132',
        PCIEnforcements::ENF132_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_email_address' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_email_address('PCI025');

    expectEnforcementToFail(
        $validator->errors,
        'PCI025',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});

it('fails rules on web_site', function () {
    $validator = new PrimaryContactInformation(['pci_web_site' => 'A123~'], 'PI');
    $validator->validate_web_site('PCI026');

    expectEnforcementToFail(
        $validator->errors,
        'PCI026',
        'ENF132',
        PCIEnforcements::ENF132_RULE['title']
    );

    $validator = new PrimaryContactInformation([
        'pci_web_site' => fake()->sentence(25),
    ], 'PI');
    $validator->validate_web_site('PCI026');

    expectEnforcementToFail(
        $validator->errors,
        'PCI026',
        'ENF148',
        PCIEnforcements::ENF148_RULE['title']
    );
});
