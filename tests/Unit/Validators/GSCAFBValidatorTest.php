<?php

/**
 * GSCAFB Information Validator Tests
 */

use App\Enforcements\GSCAFBEnforcements;
use App\Validators\GSCAFBInformation;

it('fails rules on business_name', function () {
    $validator = new GSCAFBInformation([
        'gscafb_business_name' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateBusinessName();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB001',
        'ENF026',
        GSCAFBEnforcements::ENF026_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_business_name' => 'Some business name~',
    ], 'CBA');
    $validator->validateBusinessName();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB001',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_business_name' => fake()->sentence(50),
    ], 'CBA');
    $validator->validateBusinessName();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB001',
        'ENF149',
        GSCAFBEnforcements::ENF149_RULE['title']
    );
});

it('fails rules on trading_name', function () {
    $validator = new GSCAFBInformation([
        'gscafb_trading_name' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateTradingName();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB002',
        'ENF026',
        GSCAFBEnforcements::ENF026_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_trading_name' => 'Some trading name~',
    ], 'CBA');
    $validator->validateTradingName();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB002',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_trading_name' => fake()->sentence(50),
    ], 'CBA');
    $validator->validateTradingName();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB002',
        'ENF149',
        GSCAFBEnforcements::ENF149_RULE['title']
    );
});

it('fails rules on activity_description', function () {
    $validator = new GSCAFBInformation([
        'gscafb_activity_description' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateActivityDescription();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB003',
        'ENF026',
        GSCAFBEnforcements::ENF026_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_activity_description' => 'Some trading name~',
    ], 'CBA');
    $validator->validateActivityDescription();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB003',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_activity_description' => fake()->sentence(50),
    ], 'CBA');
    $validator->validateActivityDescription();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB003',
        'ENF149',
        GSCAFBEnforcements::ENF149_RULE['title']
    );
});

it('fails rules on industry_sector_code', function () {
    $validator = new GSCAFBInformation([
        'gscafb_industry_sector_code' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateIndustrySectorCode();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_industry_sector_code' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateIndustrySectorCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB004',
        'ENF026',
        GSCAFBEnforcements::ENF026_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_industry_sector_code' => '999999',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateIndustrySectorCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB004',
        'ENF048',
        GSCAFBEnforcements::ENF048_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB004',
        'ENF138',
        GSCAFBEnforcements::ENF138_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_industry_sector_code' => '-999',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateIndustrySectorCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB004',
        'ENF116',
        GSCAFBEnforcements::ENF116_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_industry_sector_code' => '1OOO',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateIndustrySectorCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB004',
        'ENF131',
        GSCAFBEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on date_registered', function () {
    $validator = new GSCAFBInformation([
        'gscafb_date_registered' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateDateRegistered();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_date_registered' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateDateRegistered();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB005',
        'ENF026',
        GSCAFBEnforcements::ENF026_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_date_registered' => '20231231',
        'submission_date' => '20231031',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateDateRegistered();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB005',
        'ENF005',
        GSCAFBEnforcements::ENF005_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_date_registered' => '202312311',
        'submission_date' => '20231031',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateDateRegistered();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB005',
        'ENF007',
        GSCAFBEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB005',
        'ENF140',
        GSCAFBEnforcements::ENF140_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_date_registered' => '2O231231',
        'submission_date' => '20231031',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateDateRegistered();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB005',
        'ENF131',
        GSCAFBEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on business_type_code', function () {
    $validator = new GSCAFBInformation([
        'gscafb_business_type_code' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateBusinessTypeCode();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_business_type_code' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateBusinessTypeCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB006',
        'ENF026',
        GSCAFBEnforcements::ENF026_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_business_type_code' => '99O',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateBusinessTypeCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB006',
        'ENF060',
        GSCAFBEnforcements::ENF060_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB006',
        'ENF131',
        GSCAFBEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB006',
        'ENF135',
        GSCAFBEnforcements::ENF135_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_business_type_code' => '-99',
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateBusinessTypeCode();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB006',
        'ENF116',
        GSCAFBEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on surname', function () {
    $validator = new GSCAFBInformation([
        'gscafb_surname' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateSurname();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_surname' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateSurname();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB007',
        'ENF067',
        GSCAFBEnforcements::ENF067_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_surname' => 'Surname~',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateSurname();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB007',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on forename1', function () {
    $validator = new GSCAFBInformation([
        'gscafb_forename1' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateForename1();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_forename1' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateForename1();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB008',
        'ENF067',
        GSCAFBEnforcements::ENF067_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_forename1' => 'Forename~',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateForename1();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB008',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on forename2', function () {
    $validator = new GSCAFBInformation([
        'gscafb_forename2' => 'Forename~',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateForename2();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB009',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on forename3', function () {
    $validator = new GSCAFBInformation([
        'gscafb_forename3' => 'Forename~',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateForename3();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB010',
        'ENF132',
        GSCAFBEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on gender', function () {
    $validator = new GSCAFBInformation([
        'gscafb_gender' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateGender();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_gender' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateGender();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB011',
        'ENF067',
        GSCAFBEnforcements::ENF067_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_gender' => '99~',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateGender();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB011',
        'ENF086',
        GSCAFBEnforcements::ENF086_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB011',
        'ENF131',
        GSCAFBEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB011',
        'ENF134',
        GSCAFBEnforcements::ENF134_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_gender' => '-99',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateGender();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB011',
        'ENF116',
        GSCAFBEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on marital_status', function () {
    $validator = new GSCAFBInformation([
        'gscafb_marital_status' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateMaritalStatus();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_marital_status' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateMaritalStatus();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB012',
        'ENF067',
        GSCAFBEnforcements::ENF067_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_marital_status' => '99~',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateMaritalStatus();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB012',
        'ENF055',
        GSCAFBEnforcements::ENF055_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB012',
        'ENF131',
        GSCAFBEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB012',
        'ENF135',
        GSCAFBEnforcements::ENF135_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_marital_status' => '-99',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateMaritalStatus();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB012',
        'ENF116',
        GSCAFBEnforcements::ENF116_RULE['title']
    );
});

it('fails rules on date_of_birth', function () {
    $validator = new GSCAFBInformation([
        'gscafb_date_of_birth' => null,
        'borrower_classification' => '1',
    ], 'CBA');
    $validator->validateDateOfBirth();

    expect($validator->errors)->toBeEmpty()->and($validator->warnings)->toBeEmpty();

    $validator = new GSCAFBInformation([
        'gscafb_date_of_birth' => null,
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateDateOfBirth();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB013',
        'ENF067',
        GSCAFBEnforcements::ENF067_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_date_of_birth' => '20231231',
        'submission_date' => '20231031',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateDateOfBirth();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB013',
        'ENF005',
        GSCAFBEnforcements::ENF005_RULE['title']
    );
    expectEnforcementToFail(
        $validator->warnings,
        'GSCAFB013',
        'ENF168',
        GSCAFBEnforcements::ENF168_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_date_of_birth' => '202312310',
        'submission_date' => '20231031',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateDateOfBirth();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB013',
        'ENF007',
        GSCAFBEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB013',
        'ENF140',
        GSCAFBEnforcements::ENF140_RULE['title']
    );

    $validator = new GSCAFBInformation([
        'gscafb_date_of_birth' => '2O231231',
        'submission_date' => '20231031',
        'borrower_classification' => '0',
    ], 'CBA');
    $validator->validateDateOfBirth();

    expectEnforcementToFail(
        $validator->errors,
        'GSCAFB013',
        'ENF131',
        GSCAFBEnforcements::ENF131_RULE['title']
    );
});
