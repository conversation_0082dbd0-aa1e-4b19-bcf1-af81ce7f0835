<?php

use App\Services\ValidateDataService;

it('returns empty array for unknown validator', function () {
    $validatorService = new ValidateDataService;
    $validatorService->fileType = 'CBE';
    $results = $validatorService->getValidatorResults([]);

    expect($results)->toBeEmpty();
});

it('does not collect validation records without errors', function () {
    $validatorService = new ValidateDataService;
    $validatorService->fileType = 'CBE';

    $validatorService->collectValidationRecords([]);

    expect($validatorService)->failedRecords->toBeEmpty()
        ->validationsResults->toBeEmpty();
});
