<?php

use App\Enforcements\BCEnforcements;
use App\Validators\BouncedCheques;

it('passes rules for pi_identification_code', function () {
    $validator = new BouncedCheques([
        'pi_identification_code' => 'CB005',
    ]);

    $validator->validate_pi_identification_code();

    expect($validator->errors())->toBeEmpty();
});

it('fails rules on pi_identification_code', function () {
    $validator = new BouncedCheques([
        'pi_identification_code' => null,
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC001',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'pi_identification_code' => 'CB999',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC001',
        'ENF068',
        BCEnforcements::ENF068_RULE['title']
    );

    $validator = new BouncedCheques([
        'pi_identification_code' => 'CB9909~',
    ]);
    $validator->validate_pi_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC001',
        'ENF130',
        BCEnforcements::ENF130_RULE['title']
    );
});

it('passes rules on branch_identification_code', function () {
    $validator = new BouncedCheques([
        'branch_identification_code' => '001',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expect($validator->errors)->toBeEmpty();
});

it('fails rules on branch_identification_code', function () {
    $validator = new BouncedCheques([
        'branch_identification_code' => null,
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC002',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'branch_identification_code' => '-1',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC002',
        'ENF116',
        BCEnforcements::ENF116_RULE['title']
    );

    $validator = new BouncedCheques([
        'branch_identification_code' => '999',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC002',
        'ENF121',
        BCEnforcements::ENF121_RULE['title']
    );

    $validator = new BouncedCheques([
        'branch_identification_code' => 'O',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_branch_identification_code();

    expectEnforcementToFail(
        $validator->errors,
        'BC002',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on client_number', function () {
    $validator = new BouncedCheques([
        'client_number' => null,
    ]);
    $validator->validate_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC003',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'client_number' => 'SAC0192933~',
        'pi_identification_code' => 'CB005',
    ]);
    $validator->validate_client_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC003',
        'ENF132',
        BCEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on pi_client_classification', function () {
    $validator = new BouncedCheques([
        'pi_client_classification' => null,
    ]);
    $validator->validate_pi_client_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC004',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'pi_client_classification' => '99',
    ]);
    $validator->validate_pi_client_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC004',
        'ENF071',
        BCEnforcements::ENF071_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC004',
        'ENF134',
        BCEnforcements::ENF134_RULE['title']
    );

    $validator = new BouncedCheques([
        'pi_client_classification' => '-1',
    ]);
    $validator->validate_pi_client_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC004',
        'ENF116',
        BCEnforcements::ENF116_RULE['title']
    );

    $validator = new BouncedCheques([
        'pi_client_classification' => 'O',
    ]);
    $validator->validate_pi_client_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC004',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on cheque_account_reference_number', function () {
    $validator = new BouncedCheques([
        'cheque_account_reference_number' => null,
    ]);
    $validator->validate_cheque_account_reference_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC005',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_reference_number' => 'C~99',
    ]);
    $validator->validate_cheque_account_reference_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC005',
        'ENF132',
        BCEnforcements::ENF132_RULE['title']
    );
});

it('fails rules on cheque_account_opened_date', function () {
    $validator = new BouncedCheques([
        'cheque_account_opened_date' => null,
    ]);
    $validator->validate_cheque_account_opened_date();

    expectEnforcementToFail(
        $validator->errors,
        'BC006',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_opened_date' => '20231031O',
    ]);
    $validator->validate_cheque_account_opened_date();

    expectEnforcementToFail(
        $validator->errors,
        'BC006',
        'ENF007',
        BCEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC006',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC006',
        'ENF140',
        BCEnforcements::ENF140_RULE['title']
    );
});

it('fails rules on cheque_account_classification', function () {
    $validator = new BouncedCheques([
        'cheque_account_classification' => null,
    ]);
    $validator->validate_cheque_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC007',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_classification' => '99',
    ]);
    $validator->validate_cheque_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC007',
        'ENF079',
        BCEnforcements::ENF079_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC007',
        'ENF134',
        BCEnforcements::ENF134_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_classification' => '-1',
    ]);
    $validator->validate_cheque_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC007',
        'ENF116',
        BCEnforcements::ENF116_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_classification' => 'O',
    ]);
    $validator->validate_cheque_classification();

    expectEnforcementToFail(
        $validator->errors,
        'BC007',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on cheque_account_type', function () {
    $validator = new BouncedCheques([
        'cheque_account_type' => null,
    ]);
    $validator->validate_cheque_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'BC008',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_type' => '99',
    ]);
    $validator->validate_cheque_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'BC008',
        'ENF080',
        BCEnforcements::ENF080_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC008',
        'ENF134',
        BCEnforcements::ENF134_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_type' => '-1',
    ]);
    $validator->validate_cheque_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'BC008',
        'ENF116',
        BCEnforcements::ENF116_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_type' => 'O',
    ]);
    $validator->validate_cheque_account_type();

    expectEnforcementToFail(
        $validator->errors,
        'BC008',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );
});

it('fails rules on cheque_number', function () {
    $validator = new BouncedCheques([
        'cheque_number' => null,
    ]);
    $validator->validate_cheque_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC010',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_number' => '-*********',
    ]);
    $validator->validate_cheque_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC010',
        'ENF116',
        BCEnforcements::ENF116_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_number' => '*********O',
    ]);
    $validator->validate_cheque_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC010',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_number' => '102930944309400001',
    ]);
    $validator->validate_cheque_number();

    expectEnforcementToFail(
        $validator->errors,
        'BC010',
        'ENF142',
        BCEnforcements::ENF142_RULE['title']
    );
});

it('fails rules on cheque_amount', function () {
    $validator = new BouncedCheques([
        'cheque_amount' => null,
    ]);
    $validator->validate_cheque_amount();

    expectEnforcementToFail(
        $validator->errors,
        'BC011',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_amount' => '-200000',
    ]);
    $validator->validate_cheque_amount();

    expectEnforcementToFail(
        $validator->errors,
        'BC011',
        'ENF116',
        BCEnforcements::ENF116_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_amount' => '20000O',
    ]);
    $validator->validate_cheque_amount();

    expectEnforcementToFail(
        $validator->errors,
        'BC011',
        'ENF133',
        BCEnforcements::ENF133_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_amount' => '20000203820000000000003020000001',
    ]);
    $validator->validate_cheque_amount();

    expectEnforcementToFail(
        $validator->errors,
        'BC011',
        'ENF145',
        BCEnforcements::ENF145_RULE['title']
    );
});

it('fails rules on cheque_currency', function () {
    $validator = new BouncedCheques([
        'cheque_currency' => null,
    ]);
    $validator->validate_cheque_currency();

    expectEnforcementToFail(
        $validator->errors,
        'BC012',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_currency' => 'XXX~',
    ]);
    $validator->validate_cheque_currency();

    expectEnforcementToFail(
        $validator->errors,
        'BC012',
        'ENF050',
        BCEnforcements::ENF050_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC012',
        'ENF130',
        BCEnforcements::ENF130_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC012',
        'ENF136',
        BCEnforcements::ENF136_RULE['title']
    );
});

it('fails rules on cheque_bounce_date', function () {
    $validator = new BouncedCheques([
        'cheque_bounce_date' => null,
    ]);
    $validator->validate_cheque_bounce_date();

    expectEnforcementToFail(
        $validator->errors,
        'BC014',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_bounce_date' => '20231031O',
    ]);
    $validator->validate_cheque_bounce_date();

    expectEnforcementToFail(
        $validator->errors,
        'BC014',
        'ENF007',
        BCEnforcements::ENF007_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC014',
        'ENF131',
        BCEnforcements::ENF131_RULE['title']
    );
    expectEnforcementToFail(
        $validator->errors,
        'BC014',
        'ENF140',
        BCEnforcements::ENF140_RULE['title']
    );
});

it('fails rules on cheque_account_bounce_reason', function () {
    $validator = new BouncedCheques([
        'cheque_account_bounce_reason' => null,
    ]);
    $validator->validate_cheque_account_bounce_reason();

    expectEnforcementToFail(
        $validator->errors,
        'BC015',
        'ENF014',
        BCEnforcements::ENF014_RULE['title']
    );

    $validator = new BouncedCheques([
        'cheque_account_bounce_reason' => '999',
    ]);
    $validator->validate_cheque_account_bounce_reason();

    expectEnforcementToFail(
        $validator->errors,
        'BC015',
        'ENF057',
        BCEnforcements::ENF057_RULE['title']
    );
});
