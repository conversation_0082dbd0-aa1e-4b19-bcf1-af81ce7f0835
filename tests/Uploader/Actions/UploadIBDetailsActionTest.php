<?php

it('uploads IB record', function () {
    $branch = \App\Models\Validator\InstitutionBranch::factory()->create();

    app(\App\Actions\InstitutionBranch\UploadIBDetailsAction::class)->execute($branch->toArray());

    $branchRecord = $branch->only([
        'pi_identification_code',
        'branch_identification_code',
    ]);

    expect($branch->refresh())
        ->uploadstatus->toBe(1);
    $this->assertDatabaseHas('branch_information', $branchRecord, 'mysql');
    $this->assertDatabaseHas('branch_addresses', $branchRecord, 'mysql');
    $this->assertDatabaseHas('branch_telephone_numbers', $branchRecord, 'mysql');
});

it('updates existing IB record', function () {
    $branch = \App\Models\Validator\InstitutionBranch::factory()->create();
    $branchRecord = $branch->only([
        'pi_identification_code',
        'branch_identification_code',
    ]);

    // First upload a record
    app(\App\Actions\InstitutionBranch\UploadIBDetailsAction::class)->execute($branch->toArray());

    // Make some changes to the record
    $branch->branch_name = 'NTINDA';
    $branch->date_opened = '20000101';

    // Now upload the record again.
    app(\App\Actions\InstitutionBranch\UploadIBDetailsAction::class)->execute($branch->toArray());

    // Fetch the record from production
    $branchOnProduction = \App\Models\Uploader\prod\BranchInformation::query()->where($branchRecord)->first();

    expect($branchOnProduction)
        ->branch_name->toBe('NTINDA')
        ->date_opened->toBe('2000-01-01')
        ->and($branch->refresh())->uploadstatus->toBe(1);
    $this->assertDatabaseHas('branch_addresses', $branchRecord, 'mysql');
    $this->assertDatabaseHas('branch_telephone_numbers', $branchRecord, 'mysql');
});
