<?php

use App\Actions\CCG\UploadCCGDetailsAction;
use Illuminate\Support\Arr;

it('uploads CCG record', function () {
    createTable('ccg_cb005', 'CCG');

    $details = \App\Models\Validator\CollateralCreditGuarantor::factory()
        ->make(['validationstatus' => 1])
        ->toArray();

    \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('ccg_cb005')
        ->insert($details);

    $records = \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('ccg_cb005')
        ->pluck('id')
        ->all();

    app(UploadCCGDetailsAction::class)
        ->execute($records, 'ccg_cb005');

    $this->assertDatabaseHas('ccg_cb005', ['id' => 1, 'uploadstatus' => 1]);
    $this->assertDatabaseHas('collateral_credit_guarantor', Arr::only($details, [
        'pi_identification_code',
        'branch_identification_code',
        'borrowers_client_number',
        'borrower_account_reference',
    ]), 'mysql');
});

/**
 * TODO: Test that an existing record is updated during upload
 * TODO: Test that individual records are properly created or updated
 * TODO: Test that non-individual records are properly created or updated
 */
