<?php

use App\Models\Validator\CreditBorrowerAccount;
use Illuminate\Support\Arr;

it('uploads CBA record', function () {
    createTable('cba_cb005', 'CBA');

    $details = CreditBorrowerAccount::factory()
        ->make(['validationstatus' => 1])
        ->toArray();
    \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('cba_cb005')
        ->insert($details);

    $records = \Illuminate\Support\Facades\DB::connection('pgsql')->table('cba_cb005')->pluck('id')->all();

    app(\App\Actions\CreditBorrowerAccount\UploadCBADetailsAction::class)->execute($records, 'cba_cb005');

    $this->assertDatabaseHas('cba_cb005', ['id' => 1, 'uploadstatus' => 1]);
    $this->assertDatabaseHas('credit_borrower_account', Arr::only($details, [
        'pi_identification_code',
        'branch_identification_code',
        'borrowers_client_number',
        'credit_account_reference',
    ]), 'mysql');
});

/**
 * TODO: Test that an existing record is updated during upload
 * TODO: Test that individual records are properly created or updated
 * TODO: Test that non-individual records are properly created or updated
 */
