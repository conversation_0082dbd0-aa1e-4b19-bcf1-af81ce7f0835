<?php

use App\Actions\CreditApplication\UploadCADetailsAction;
use App\Models\Validator\CreditApplication;

it('uploads CAP record', function () {
    createTable('cap_cb005', 'CAP');

    $details = CreditApplication::factory()
        ->make(['validationstatus' => 1])
        ->toArray();
    $cap = new CreditApplication;
    $cap->setTable('cap_cb005');
    $cap->setConnection('pgsql');
    $cap->primaryKey = 'id';
    $cap->fill($details)->save();

    $records = collect([$cap->getAttributes()]);

    app(UploadCADetailsAction::class)->execute($records, 'cap_cb005');

    expect($cap->refresh())->uploadstatus->toBe(1);
    $this->assertDatabaseHas('credit_applications', $cap->only([
        'pi_identification_code',
        'branch_identification_code',
        'client_number',
    ]), 'mysql');
});

/**
 * TODO: Test that an existing record is updated during upload
 * TODO: Test that individual records are properly created or updated
 * TODO: Test that non-individual records are properly created or updated
 */
