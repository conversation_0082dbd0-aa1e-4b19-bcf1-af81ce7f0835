<?php

use App\Actions\ParticipatingInstitutionStakeholders\UploadPISDetailsAction;
use App\Models\Uploader\prod\PIStakeholder;
use App\Models\Validator\ParticipatingInstitutionStakeholder;

it('uploads pis record', function () {
    $pi = ParticipatingInstitutionStakeholder::factory()->create();

    app(UploadPISDetailsAction::class)->execute($pi->toArray());

    expect($pi->refresh())
        ->uploadstatus->toBe(1);
});

it('updates existing pis record', function () {
    $pis = \App\Models\Validator\ParticipatingInstitutionStakeholder::factory()->create();
    app(UploadPISDetailsAction::class)->execute($pis->toArray());

    $pis->ei_employer_name = 'STANBIC BANK';
    $pis->ei_employee_number = '123454';

    // Now repeat the same action, so will the record be updated or a new one created or something else?
    app(UploadPISDetailsAction::class)->execute($pis->toArray());

    $piOnProduction = PIStakeholder::query()->where('pi_identification_code', $pis->pi_identification_code)->first();

    expect($piOnProduction)
        ->not->toBeNull()
        ->and($pis->fresh())
        ->uploadstatus->toBe(1);

    $this->assertDatabaseHas('employment_info', [
        'employer_name' => 'STANBIC BANK',
        'employee_number' => '123454',
    ], 'mysql');
});
