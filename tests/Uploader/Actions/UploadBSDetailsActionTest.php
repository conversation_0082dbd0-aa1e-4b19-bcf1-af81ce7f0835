<?php

it('uploads BS record', function () {
    $bsRecords = \App\Models\Validator\BorrowerStakeholder::factory()->count(3)->create([
        'borrowers_client_number' => mt_rand(100, 900),
        'validationstatus' => 1,
    ]);

    app(\App\Actions\BS\UploadBSDetailsAction::class)->execute($bsRecords->pluck('id')->toArray());

    expect($bsRecords)
        ->each(function ($bs) {
            $bs->refresh()->uploadstatus->toBe(1);
        });

    foreach ($bsRecords as $bs) {
        $bsOnProduction = \App\Models\Uploader\prod\BorrowerStakeholder::query()->where($bs->only([
            'pi_identification_code',
            'branch_identification_code',
            'borrowers_client_number',
        ]))->first();

        expect($bsOnProduction)->not->toBeNull();
    }
});

/**
 * TODO: Test that an existing record is updated during upload
 * TODO: Test that individual records are properly created or updated
 * TODO: Test that non-individual records are properly created or updated
 */
