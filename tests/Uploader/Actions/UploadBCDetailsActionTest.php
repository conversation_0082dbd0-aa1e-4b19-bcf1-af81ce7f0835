<?php

it('uploads BC record', function () {
    $records = \App\Models\Validator\BouncedCheque::factory()->count(3)->create([
        'client_number' => mt_rand(100, 900),
        'validationstatus' => 1,
    ]);

    app(\App\Actions\BC\UploadBCDetailsAction::class)->execute($records->pluck('id')->toArray());

    expect($records)
        ->each(function ($bs) {
            $bs->refresh()->uploadstatus->toBe(1);
        });

    foreach ($records as $bc) {
        $bcOnProduction = \App\Models\Uploader\prod\BouncedCheque::query()->where($bc->only([
            'pi_identification_code',
            'branch_identification_code',
            'client_number',
        ]))->first();

        expect($bcOnProduction)->not->toBeNull();
    }
});

/**
 * TODO: Test that an existing record is updated during upload
 * TODO: Test that individual records are properly created or updated
 * TODO: Test that non-individual records are properly created or updated
 */
