<?php

use App\Events\FileValidationCompleted;
use App\Listeners\NotifyPIUsers;
use App\Models\User;
use App\Models\Validator\DataSubmission;

it('fires job to notify users', function () {
    $user = User::factory()->create(['pi_code' => 'CB005']);

    Notification::fake();

    $listener = new NotifyPIUsers;
    $listener->handle(
        new FileValidationCompleted('CB005', '20231031')
    );

    Notification::assertSentTo($user, function (\App\Notifications\ValidationCompleted $notification, array $channels) use ($user) {
        return $notification->toMail($user)->greeting = 'Hello '.$user->name && $notification->toArray($user) === [];
    });
});

it('does not notify without users', function () {
    Notification::fake();

    $listener = new NotifyPIUsers;
    $listener->handle(
        new FileValidationCompleted('CB005', '20231031')
    );

    Notification::assertNothingSent();
});

it('does not notify with pending validation', function () {
    DataSubmission::factory()->create([
        'processing' => 1,
        'pi_identification_code' => 'CB005',
        'submission_date' => '20231031',
    ]);
    Notification::fake();

    $listener = new NotifyPIUsers;
    $isQueued = $listener->shouldQueue(
        new FileValidationCompleted('CB005', '20231031')
    );

    expect($isQueued)->toBeFalse();
    Notification::assertNothingSent();
});
