<?php

use App\Models\Validator\CollateralMaterialCollateral;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('runs CMC validator command successfully', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CMC',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005', 'CBA');
    $cbaRecord = CreditBorrowerAccount::factory()->makeOne(['validationstatus' => 1])->toArray();
    insertCreatedTable('cba_cb005', $cbaRecord);
    createTable('cmc_cb005', 'CMC');
    // Creates a valid PI record expected to pass all checks
    $cmcIdentityDetails = \Illuminate\Support\Arr::only($cbaRecord, [
        'pi_identification_code',
        'branch_identification_code',
        'borrowers_client_number',
    ]);
    $cmcIdentityDetails['borrower_account_reference'] = $cbaRecord['credit_account_reference'];

    $cmcRecord = CollateralMaterialCollateral::factory()->makeOne($cmcIdentityDetails)->toArray();
    $recordId = insertCreatedTable('cmc_cb005', $cmcRecord);

    $this->artisan('dwh:validate-cmc CB005')->assertSuccessful();

    $record = findRecord($recordId, 'cmc_cb005');

    expect($record)
        ->validationstatus->toBe(1)
        ->and($submission->fresh())
        ->validationstatus->toBe(1);
});
