<?php

use App\Models\Validator\DataSubmission;
use App\Models\Validator\ParticipatingInstitutionStakeholder;

it('runs PIS validator command successfully', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'PIS',
        'pi_identification_code' => 'CB005',
    ]);
    $record = ParticipatingInstitutionStakeholder::factory()->create();

    $this->artisan('dwh:validate-pis CB005')->assertSuccessful();

    expect($record->fresh())
        ->validationstatus->toBe(1)
        ->and($submission->fresh())
        ->validationstatus->toBe(1);
});
