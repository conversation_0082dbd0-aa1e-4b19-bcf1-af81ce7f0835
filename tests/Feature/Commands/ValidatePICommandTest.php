<?php

use App\Models\Validator\DataSubmission;
use App\Models\Validator\ParticipatingInstitution;

it('runs PI validator command successfully', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'PI',
        'pi_identification_code' => 'CB005',
    ]);
    $record = ParticipatingInstitution::factory()->create();

    $this->artisan('dwh:validate-pi CB005')->assertSuccessful();

    expect($record->fresh())
        ->validationstatus->toBe(1)
        ->and($submission->fresh())
        ->validationstatus->toBe(1);
});
