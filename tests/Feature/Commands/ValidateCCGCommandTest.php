<?php

use App\Models\Validator\CollateralCreditGuarantor;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('runs CCG validator command successfully', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005', 'CBA');
    $cbaRecord = CreditBorrowerAccount::factory()->makeOne(['validationstatus' => 1])->toArray();
    insertCreatedTable('cba_cb005', $cbaRecord);
    createTable('ccg_cb005', 'CCG');
    // Creates a valid PI record expected to pass all checks
    $ccgIdentityDetails = \Illuminate\Support\Arr::only($cbaRecord, [
        'pi_identification_code',
        'branch_identification_code',
        'borrowers_client_number',
    ]);
    $ccgIdentityDetails['borrower_account_reference'] = $cbaRecord['credit_account_reference'];

    $ccgRecord = CollateralCreditGuarantor::factory()->makeOne($ccgIdentityDetails)->toArray();
    $recordId = insertCreatedTable('ccg_cb005', $ccgRecord);

    $this->artisan('dwh:validate-ccg CB005')->assertSuccessful();

    $record = findRecord($recordId, 'ccg_cb005');

    expect($record)
        ->validationstatus->toBe(1)
        ->and($submission->fresh())
        ->validationstatus->toBe(1);
});
