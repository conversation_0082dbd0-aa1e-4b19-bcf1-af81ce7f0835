<?php

use App\Jobs\Uploader\BorrowerStakeholder\UpdateBSDetailsJob;
use <PERSON><PERSON>y\MockInterface;

it('executes action to upload BS record', function () {
    $record = \App\Models\Validator\BorrowerStakeholder::factory()->create();
    $mock = $this->mock(\App\Actions\BS\UploadBSDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute')->once();
    });
    [$job, $batch] = (new UpdateBSDetailsJob(collect($record->id)))->withFakeBatch();

    $job->handle($mock);

    $mock->shouldHaveReceived('execute');
});
