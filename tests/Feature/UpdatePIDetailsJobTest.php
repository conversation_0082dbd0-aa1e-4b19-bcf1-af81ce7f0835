<?php

use App\Actions\ParticipatingInstitution\UploadPIDetailsAction;
use App\Jobs\Uploader\ParticipatingInstitutions\UpdatePIDetailsJob;
use Illuminate\Support\Facades\Queue;
use Mo<PERSON>y\MockInterface;

it('does not dispatch PI job when batch is cancelled', function () {
    $pi = \App\Models\Validator\ParticipatingInstitution::factory()->create();
    $mock = $this->mock(UploadPIDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('execute');
    });
    Queue::fake();

    [$job, $batch] = (new UpdatePIDetailsJob($pi->toArray()))->withFakeBatch();

    $batch->cancel();
    $job->handle($mock);

    Queue::assertNotPushed(UpdatePIDetailsJob::class);
    expect($pi->refresh())->uploadstatus->toBe(0)
        ->and($mock)->shouldNotHaveReceived('execute');
});

it('executes action to upload PI record', function () {
    $pi = \App\Models\Validator\ParticipatingInstitution::factory()->create();
    $mock = $this->mock(UploadPIDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute')->once();
    });
    [$job, $batch] = (new UpdatePIDetailsJob($pi->toArray()))->withFakeBatch();

    $job->handle($mock);

    $mock->shouldHaveReceived('execute');
});
