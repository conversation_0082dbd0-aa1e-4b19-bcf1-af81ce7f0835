<?php

use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('does nothing without change in account number', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    // Current Month
    $details = CreditBorrowerAccount::factory()->make()->toArray();
    $record = new CreditBorrowerAccount;
    $record->setTable($submission->table_name);
    $record->setConnection('pgsql');
    $record->fill($details)->save();
    // Previous Month
    $details['submission_date'] = \Illuminate\Support\Carbon::createFromFormat('Ymd', $submission->submission_date)
        ->startOfMonth()->subMonth()->endOfMonth()->format('Ymd');
    $details['validationstatus'] = 1;
    $oldRecord = new CreditBorrowerAccount;
    $oldRecord->setTable($submission->table_name);
    $oldRecord->setConnection('pgsql');
    $oldRecord->fill($details)->save();

    app(\App\Actions\Validator\ValidateForCBAChangedAccountNumberAction::class)->execute($submission);

    expect($record->refresh())->validationstatus->toBe(0); // Not changed
    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('records validation errors for changed account number', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    // Current Month
    $details = CreditBorrowerAccount::factory()->make()->toArray();
    $details['credit_account_reference'] = '*************';
    $details['old_account_number'] = null; // Ensure this is always null to trigger the validation error.
    $record = new CreditBorrowerAccount;
    $record->setTable($submission->table_name);
    $record->setConnection('pgsql');
    $record->fill($details)->save();
    // Previous Month
    $details['submission_date'] = \Illuminate\Support\Carbon::createFromFormat('Ymd', $submission->submission_date)
        ->startOfMonth()->subMonth()->endOfMonth()->format('Ymd');
    $details['validationstatus'] = 1;
    $details['credit_account_reference'] = '*************';
    $oldRecord = new CreditBorrowerAccount;
    $oldRecord->setTable($submission->table_name);
    $oldRecord->setConnection('pgsql');
    $oldRecord->fill($details)->save();

    app(\App\Actions\Validator\ValidateForCBAChangedAccountNumberAction::class)->execute($submission);

    /**
     * We expect the validation error to be recorded but not invalidated
     * so that the general validations also process this record.
     *
     * But then what if this record does not have any further validation errors?
     * We need to implement away to come back and invalidate this record. :smile
     */
    expect($record->fresh())->validationstatus->toBe(0);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CBA',
        'enforcement_code' => 'ENF024',
        'control_code' => 'CBA038',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});

it('passes validation when account number has expected change', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    // Current Month
    $details = CreditBorrowerAccount::factory()->make()->toArray();
    $details['credit_account_reference'] = '*************';
    $details['old_account_number'] = '*************'; // Ensure this is always null to trigger the validation error.
    $record = new CreditBorrowerAccount;
    $record->setTable($submission->table_name);
    $record->setConnection('pgsql');
    $record->fill($details)->save();
    // Previous Month
    $details['submission_date'] = \Illuminate\Support\Carbon::createFromFormat('Ymd', $submission->submission_date)
        ->startOfMonth()->subMonth()->endOfMonth()->format('Ymd');
    $details['validationstatus'] = 1;
    $details['credit_account_reference'] = '*************';
    $oldRecord = new CreditBorrowerAccount;
    $oldRecord->setTable($submission->table_name);
    $oldRecord->setConnection('pgsql');
    $oldRecord->fill($details)->save();

    app(\App\Actions\Validator\ValidateForCBAChangedAccountNumberAction::class)->execute($submission);

    /**
     * We expect the validation error not to be recorded and the record remains not validated
     * so that the general validations also process this same record.
     *
     * But then what if this record does not have any further validation errors?
     * We need to implement away to come back and mark this record as validated.
     */
    expect($record->fresh())->validationstatus->toBe(0);
    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});
