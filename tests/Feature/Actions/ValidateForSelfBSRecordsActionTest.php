<?php

use App\Actions\Validator\ValidateForSelfBSRecordsAction;
use App\Models\Validator\BorrowerStakeholder;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('does nothing when no self bs exist', function () {
    BorrowerStakeholder::factory()->create();
    $submission = DataSubmission::factory()->create(['file_identifier' => 'BS', 'pi_identification_code' => 'CB005']);
    createTable('cba_cb005');
    app(ValidateForSelfBSRecordsAction::class)->execute($submission);

    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('invalidates self bs records', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'BS',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    $bs = BorrowerStakeholder::factory()->create([
        'submission_date' => $submission->submission_date,
    ]);
    $details = CreditBorrowerAccount::factory()->make([
        'borrower_client_number' => $bs->borrower_client_number,
        'branch_identification_code' => $bs->branch_identification_code,
        'submission_date' => $submission->submission_date,
        'validationstatus' => 1,
        'ii_registration_certificate_number' => $bs->ii_registration_certificate_number,
        'ii_fcs_number' => $bs->ii_fcs_number,
        'ii_value_added_tax_number' => $bs->ii_value_added_tax_number,
        'ii_drivers_license_permit_number' => $bs->ii_drivers_license_permit_number,
        'ii_nssf_number' => $bs->ii_nssf_number,
        'ii_country_id' => $bs->ii_country_id,
        'ii_police_id_number' => $bs->ii_police_id_number,
        'ii_updf_number' => $bs->ii_updf_number,
        'ii_public_service_pension_number' => $bs->ii_public_service_pension_number,
        'ii_kacita_license_number' => $bs->ii_kacita_license_number,
        'ii_teacher_registration_number' => $bs->ii_teacher_registration_number,
    ])->toArray();
    $record = new CreditBorrowerAccount;
    $record->setTable($submission->cba_table_name);
    $record->setConnection('pgsql');
    $record->fill($details)->save();

    app(ValidateForSelfBSRecordsAction::class)->execute($submission);

    expect($bs->fresh())->validationstatus->toBe(2);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'BS',
        'enforcement_code' => 'ENF122',
        'control_code' => 'BS003',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
