<?php

use App\Actions\Validator\ValidateForOrphanedCMCRecordsAction;
use App\Models\Validator\CollateralMaterialCollateral;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;
use Illuminate\Support\Facades\DB;

it('does nothing when no cmc orphans exist', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CMC',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    createTable('cmc_cb005');

    $cbaDetails = CreditBorrowerAccount::factory()
        ->make(['validationstatus' => 1, 'submission_date' => $submission->submission_date])
        ->toArray();
    DB::connection('pgsql')
        ->table('cba_cb005')
        ->insert($cbaDetails);

    $cmcDetails = CollateralMaterialCollateral::factory()
        ->make([
            'borrower_account_reference' => $cbaDetails['credit_account_reference'],
            'borrowers_client_number' => $cbaDetails['borrowers_client_number'],
            'branch_identification_code' => $cbaDetails['branch_identification_code'],
            'submission_date' => $cbaDetails['submission_date'],
        ])
        ->toArray();

    DB::connection('pgsql')
        ->table('cmc_cb005')
        ->insert($cmcDetails);

    app(ValidateForOrphanedCMCRecordsAction::class)->execute($submission);

    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('records orhaned cmc validation errors', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CMC',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    createTable('cmc_cb005');
    $cmcDetails = CollateralMaterialCollateral::factory()
        ->make()
        ->toArray();

    DB::connection('pgsql')
        ->table('cmc_cb005')
        ->insert($cmcDetails);

    app(ValidateForOrphanedCMCRecordsAction::class)->execute($submission);

    $record = DB::table('cmc_cb005')->where([
        'borrower_account_reference' => $cmcDetails['borrower_account_reference'],
        'borrowers_client_number' => $cmcDetails['borrowers_client_number'],
        'branch_identification_code' => $cmcDetails['branch_identification_code'],
        'submission_date' => $cmcDetails['submission_date'],
    ])->first();

    expect($record)->validationstatus->toBe(2);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CMC',
        'enforcement_code' => 'ENF094',
        'control_code' => 'CMC001',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CMC',
        'enforcement_code' => 'ENF174',
        'control_code' => 'CMC001',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
