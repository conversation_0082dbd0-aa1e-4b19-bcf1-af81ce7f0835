<?php

use App\Actions\Validator\ValidateForDuplicateCBARecordsAction;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('does nothing when no cba duplicates exist', function () {
    $submission = DataSubmission::factory()->create(['file_identifier' => 'CBA', 'pi_identification_code' => 'CB005']);
    createTable('cba_cb005');
    $details = CreditBorrowerAccount::factory()->make()->toArray();
    $record = new CreditBorrowerAccount;
    $record->setTable($submission->table_name);
    $record->setConnection('pgsql');
    $record->fill($details)->save();

    app(ValidateForDuplicateCBARecordsAction::class)->execute($submission);

    expect($record->refresh())->validationstatus->toBe(0); // Not changed
    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('records duplicate cba validation errors', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');

    $records = CreditBorrowerAccount::factory()->count(2)->make();

    foreach ($records as $record) {
        $record->setTable($submission->table_name);
        $record->setConnection('pgsql');
        $record->save();
    }

    app(ValidateForDuplicateCBARecordsAction::class)->execute($submission);

    expect($records->last()->fresh())
        ->validationstatus->toBe(2)
        ->and($records->first()->fresh())
        ->validationstatus->toBe(0);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CBA',
        'enforcement_code' => 'ENF125',
        'control_code' => 'CBA005',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
