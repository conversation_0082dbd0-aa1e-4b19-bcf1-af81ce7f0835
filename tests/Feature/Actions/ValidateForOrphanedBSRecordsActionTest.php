<?php

use App\Actions\Validator\ValidateForOrphanedBSRecordsAction;
use App\Models\Validator\BorrowerStakeholder;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('does nothing when no bs orphans exist', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'BS',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005', 'CBA');

    $details = CreditBorrowerAccount::factory()
        ->make(['validationstatus' => 1, 'submission_date' => $submission->submission_date])
        ->toArray();
    \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('cba_cb005')
        ->insert($details);
    BorrowerStakeholder::factory()->create([
        'borrowers_client_number' => $details['borrowers_client_number'],
        'branch_identification_code' => $details['branch_identification_code'],
        'submission_date' => $details['submission_date'],
    ]);

    app(ValidateForOrphanedBSRecordsAction::class)->execute($submission);

    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('records orhaned bs validation errors', function () {
    createTable('cba_cb005', 'CBA');
    $record = BorrowerStakeholder::factory()->create();
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'BS',
        'pi_identification_code' => 'CB005',
    ]);

    app(ValidateForOrphanedBSRecordsAction::class)->execute($submission);

    expect($record->fresh())
        ->validationstatus->toBe(2);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'BS',
        'enforcement_code' => 'ENF094',
        'control_code' => 'BS001',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
