<?php

use App\Actions\Validator\ValidateForDuplicateCAPRecordsAction;
use App\Models\Validator\CreditApplication;
use App\Models\Validator\DataSubmission;

it('does nothing when no cap duplicates exist', function () {
    $submission = DataSubmission::factory()->create(['file_identifier' => 'CAP', 'pi_identification_code' => 'CB005']);
    createTable('cap_cb005', 'CAP');
    $details = CreditApplication::factory()->make()->toArray();
    $cap = new CreditApplication;
    $cap->setTable($submission->table_name);
    $cap->setConnection('pgsql');
    $cap->primaryKey = 'id';
    $cap->fill($details)->save();

    app(ValidateForDuplicateCAPRecordsAction::class)->execute($submission);

    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('records duplicate cap validation errors', function () {
    $records = CreditApplication::factory()->count(2)->make();
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CAP',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cap_cb005', 'CAP');

    foreach ($records as $record) {
        $record->setTable($submission->table_name);
        $record->setConnection('pgsql');
        $record->primaryKey = 'id';
        $record->save();
    }

    app(ValidateForDuplicateCAPRecordsAction::class)->execute($submission);

    expect($records->last()->fresh())
        ->validationstatus->toBe(2)
        ->and($records->first()->fresh())
        ->validationstatus->toBe(0);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CAP',
        'enforcement_code' => 'ENF124',
        'control_code' => 'CAP004',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
