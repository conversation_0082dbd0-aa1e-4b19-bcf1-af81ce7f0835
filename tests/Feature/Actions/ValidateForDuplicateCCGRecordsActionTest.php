<?php

use App\Actions\Validator\ValidateForDuplicateCCGRecordsAction;
use App\Models\Validator\CollateralCreditGuarantor;
use App\Models\Validator\DataSubmission;

it('does nothing when no ccg duplicates exist', function () {
    $submission = DataSubmission::factory()->create(['file_identifier' => 'CCG', 'pi_identification_code' => 'CB005']);
    createTable('ccg_cb005');
    $details = CollateralCreditGuarantor::factory()->make()->toArray();
    $ccg = new CollateralCreditGuarantor;
    $ccg->setTable($submission->table_name);
    $ccg->setConnection('pgsql');
    $ccg->primaryKey = 'id';
    $ccg->fill($details)->save();

    app(ValidateForDuplicateCCGRecordsAction::class)->execute($submission);

    expect($ccg->refresh())->validationstatus->toBe(0); // Not changed
    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('records duplicate ccg validation errors', function () {
    $records = CollateralCreditGuarantor::factory()->count(2)->make();
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('ccg_cb005');

    foreach ($records as $record) {
        $record->setTable($submission->table_name);
        $record->setConnection('pgsql');
        $record->primaryKey = 'id';
        $record->save();
    }

    app(ValidateForDuplicateCCGRecordsAction::class)->execute($submission);

    expect($records->last()->fresh())
        ->validationstatus->toBe(2)
        ->and($records->first()->fresh())
        ->validationstatus->toBe(0);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CCG',
        'enforcement_code' => 'ENF126',
        'control_code' => 'CCG004',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
