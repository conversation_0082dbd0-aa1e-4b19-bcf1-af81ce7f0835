<?php

use App\Actions\Validator\ValidateForSelfCCGRecordsAction;
use App\Models\Validator\CollateralCreditGuarantor;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\DataSubmission;

it('does nothing when no self ccg exist', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    createTable('ccg_cb005');

    $cbaDetails = CreditBorrowerAccount::factory()
        ->make(['validationstatus' => 1, 'submission_date' => $submission->submission_date])
        ->toArray();
    DB::connection('pgsql')
        ->table('cba_cb005')
        ->insert($cbaDetails);
    $ccgKeyDetails = [
        'borrower_account_reference' => $cbaDetails['credit_account_reference'],
        'borrowers_client_number' => $cbaDetails['borrowers_client_number'],
        'branch_identification_code' => $cbaDetails['branch_identification_code'],
        'submission_date' => $cbaDetails['submission_date'],
    ];
    $ccgDetails = CollateralCreditGuarantor::factory()
        ->make($ccgKeyDetails)
        ->toArray();

    DB::connection('pgsql')
        ->table('ccg_cb005')
        ->insert($ccgDetails);
    app(ValidateForSelfCCGRecordsAction::class)->execute($submission);

    $ccgRecord = DB::connection('pgsql')->table('ccg_cb005')->where($ccgKeyDetails)->first();

    expect($ccgRecord)->validationstatus->toBe(0);
    $this->assertDatabaseEmpty('failed_validations', 'pgsql_submissions');
});

it('invalidates self ccg records', function () {
    $submission = DataSubmission::factory()->create([
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ]);
    createTable('cba_cb005');
    createTable('ccg_cb005');

    $ccg = CollateralCreditGuarantor::factory()->make([
        'submission_date' => $submission->submission_date,
    ]);
    DB::connection('pgsql')->table('ccg_cb005')->insert($ccg->toArray());
    $details = CreditBorrowerAccount::factory()->make([
        'borrower_client_number' => $ccg->borrower_client_number,
        'branch_identification_code' => $ccg->branch_identification_code,
        'submission_date' => $submission->submission_date,
        'validationstatus' => 1,
        'ii_registration_certificate_number' => $ccg->ii_registration_certificate_number,
        'ii_fcs_number' => $ccg->ii_fcs_number,
        'ii_value_added_tax_number' => $ccg->ii_value_added_tax_number,
        'ii_drivers_license_permit_number' => $ccg->ii_drivers_license_permit_number,
        'ii_nssf_number' => $ccg->ii_nssf_number,
        'ii_country_id' => $ccg->ii_country_id,
        'ii_police_id_number' => $ccg->ii_police_id_number,
        'ii_updf_number' => $ccg->ii_updf_number,
        'ii_public_service_pension_number' => $ccg->ii_public_service_pension_number,
        'ii_kacita_license_number' => $ccg->ii_kacita_license_number,
        'ii_teacher_registration_number' => $ccg->ii_teacher_registration_number,
    ])->toArray();
    $record = new CreditBorrowerAccount;
    $record->setTable($submission->cba_table_name);
    $record->setConnection('pgsql');
    $record->fill($details)->save();

    app(ValidateForSelfCCGRecordsAction::class)->execute($submission);

    $ccgRecord = DB::connection('pgsql')->table('ccg_cb005')->where([
        'borrowers_client_number' => $ccg->borrowers_client_number,
        'branch_identification_code' => $ccg->branch_identification_code,
        'submission_date' => $submission->submission_date,
    ])->first();
    expect($ccgRecord)->validationstatus->toBe(2);
    $this->assertDatabaseHas('failed_validations', [
        'filename' => 'CCG',
        'enforcement_code' => 'ENF127',
        'control_code' => 'CCG003',
        'pi_identification_code' => 'CB005',
    ], 'pgsql_submissions');
});
