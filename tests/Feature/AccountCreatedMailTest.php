<?php

use App\Mail\AccountCreated;

it('has correct in details account created email', function () {
    $user = \App\Models\User::factory()->create([
        'name' => '<PERSON> Doe',
        'email' => '<EMAIL>',
    ]);

    $mailable = new AccountCreated($user, str_random(8));

    $mailable->assertHasSubject('Account created for gnuGrid CRB Submissions Portal')
        ->assertSeeInHtml('<PERSON> Doe');
});
