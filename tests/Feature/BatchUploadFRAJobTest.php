<?php

use App\Jobs\Uploader\FinancialMalpractice\BatchUploadFRAJob;
use App\Jobs\Uploader\FinancialMalpractice\UpdateFRADetailsJob;
use App\Models\Validator\FinancialMalpracticeData;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload FRA data', function () {
    Queue::fake();

    FinancialMalpracticeData::factory()->create([
        'validationstatus' => 1,
    ]);

    (new BatchUploadFRAJob)->handle();

    Queue::assertPushed(UpdateFRADetailsJob::class);
});

it('dispatches jobs to reload FRA data', function () {
    Queue::fake();

    FinancialMalpracticeData::factory()->create([
        'validationstatus' => 1,
        'uploadstatus' => 3,
    ]);

    $job = new BatchUploadFRAJob;
    $job->reload();
    $job->handle();

    Queue::assertPushed(UpdateFRADetailsJob::class);
});

it('adds job to FRA batch when batch exists', function () {
    $pi = \App\Models\Validator\FinancialMalpracticeData::factory()->create([
        'validationstatus' => 1,
    ]);
    Queue::fake();

    [$job, $batch] = (new BatchUploadFRAJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdateFRADetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});
