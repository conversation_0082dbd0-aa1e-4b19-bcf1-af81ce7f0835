<?php

use App\Jobs\Uploader\CreditBorrowerAccount\BatchInstitutionCBADataJob;
use App\Jobs\Uploader\CreditBorrowerAccount\BatchUploadCBAJob;
use App\Models\Validator\DataSubmission;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload CBA data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCBAJob)->handle();

    Queue::assertPushed(BatchInstitutionCBADataJob::class);
});

it('dispatches jobs to reload CBA data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCBAJob)
        ->reload()
        ->handle();

    Queue::assertPushed(BatchInstitutionCBADataJob::class, function (BatchInstitutionCBADataJob $job) {
        return $job->uploadStatus === 3;
    });
});

it('adds job to CBA batch when batch exists', function () {
    $submissionRecord = [
        'file_identifier' => 'CBA',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    Queue::fake();

    [$job, $batch] = (new BatchUploadCBAJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(BatchInstitutionCBADataJob::class);
    expect($batch)->totalJobs->toBe(1);
});
