<?php

use App\Events\FileValidationCompleted;
use App\Jobs\UI\Reports\ZipValidationReportsJob;
use App\Models\Validator\DataSubmission;

it('fires job to zip validation reports', function () {
    Queue::fake();

    $zipValidationReportListener = new \App\Listeners\ZipValidationReports;
    $zipValidationReportListener->handle(
        new FileValidationCompleted('CB005', '20231031')
    );

    Queue::assertPushed(ZipValidationReportsJob::class);
});

it('does not fire zip job with pending validation', function () {
    DataSubmission::factory()->create([
        'processing' => 1,
        'pi_identification_code' => 'CB005',
        'submission_date' => '20231031',
    ]);
    Queue::fake();

    $zipValidationReportListener = new \App\Listeners\ZipValidationReports;
    $isQueued = $zipValidationReportListener->shouldQueue(
        new FileValidationCompleted('CB005', '20231031')
    );

    expect($isQueued)->toBeFalse();
    Queue::assertNothingPushed();
});
