<?php

use App\Actions\CCG\UploadCCGDetailsAction;
use App\Jobs\Uploader\CollateralCreditGuarantor\UpdateCCGDetailsJob;
use <PERSON><PERSON>y\MockInterface;

it('does not dispatch CCG details job when batch is cancelled', function () {
    $mock = $this->mock(UploadCCGDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('execute');
    });

    [$job, $batch] = (new UpdateCCGDetailsJob(collect([]), 'cap_cb005'))->withFakeBatch();

    $batch->cancel();
    $job->handle($mock);

    expect($mock)->shouldNotHaveReceived('execute');
});

it('executes action to upload CAP record', function () {
    $mock = $this->mock(UploadCCGDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute');
    });

    [$job, $batch] = (new UpdateCCGDetailsJob(collect([]), 'cap_cb005'))->withFakeBatch();

    $job->handle($mock);

    $mock->shouldHaveReceived('execute');
});
