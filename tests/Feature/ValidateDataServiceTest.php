<?php

use App\Services\ValidateDataService;

it('collects failed validation records', function () {
    $submission = \App\Models\Validator\DataSubmission::factory()->create([
        'file_identifier' => 'BS',
    ]);
    $record = \App\Models\Validator\BorrowerStakeholder::factory()->create([
        'validationstatus' => 0,
        'submission_date' => '20231031',
        'gscafb_date_of_birth' => '20100101',
        'borrowers_client_number' => '',
    ]);

    $validatorService = new ValidateDataService;
    $validatorService->use($submission);
    $validatorService->collectValidationRecords($record->toArray());

    expect($validatorService)->failedRecords->not->toBeEmpty()
        ->validationsResults->not->toBeEmpty()
        ->successfulRecords->toBeEmpty();
});
