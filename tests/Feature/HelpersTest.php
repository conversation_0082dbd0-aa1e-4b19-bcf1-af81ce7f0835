<?php

it('saves upload progress', function () {
    cache()->forget('upload_progress');

    save_upload_progress(10, 'Uploading CBA');

    $progress = cache('upload_progress');

    expect($progress)
        ->general_progress->toBeArray()
        ->general_progress->progress->toBe(10)
        ->general_progress->message->toBe('Uploading CBA');

    save_upload_progress(50, 'Uploading CCG');

    $progress = cache('upload_progress');

    expect($progress)
        ->general_progress->toBeArray()
        ->general_progress->progress->toBe(50)
        ->general_progress->message->toBe('Uploading CCG');
});

it('saves validation progress', function () {
    cache()->forget('CB005_validation_progress');

    save_progress('CB005', 10, 'Validating');

    $progress = cache('CB005_validation_progress');

    expect($progress)
        ->general_progress->toBeArray()
        ->general_progress->progress->toBe(10)
        ->general_progress->message->toBe('Validating');

    save_progress('CB005', 50, 'Validating');

    $progress = cache('CB005_validation_progress');

    expect($progress)
        ->general_progress->toBeArray()
        ->general_progress->progress->toBe(50)
        ->general_progress->message->toBe('Validating');
});
