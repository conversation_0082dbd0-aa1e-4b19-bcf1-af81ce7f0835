<?php

use <PERSON><PERSON><PERSON>\MockInterface;

it('does not dispatch download CAP job when batch is cancelled', function () {
    $mock = $this->mock(\App\Actions\CreditApplication\DownloadOnlineCADetailsAction::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('execute');
    });

    [$job, $batch] = (new \App\Jobs\Uploader\CreditApplication\DownloadOnlineCreditApplicationJob(collect([])))->withFakeBatch();

    $batch->cancel();
    $job->handle($mock);

    expect($mock)->shouldNotHaveReceived('execute');
});

it('executes action to download online CAP record', function () {
    $mock = $this->mock(\App\Actions\CreditApplication\DownloadOnlineCADetailsAction::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute');
    });

    [$job, $batch] = (new \App\Jobs\Uploader\CreditApplication\DownloadOnlineCreditApplicationJob(collect([])))->withFakeBatch();

    $job->handle($mock);

    $mock->shouldHaveReceived('execute');
});
