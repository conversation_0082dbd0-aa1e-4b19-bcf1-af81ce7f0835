<?php

use App\Jobs\Uploader\BouncedCheque\BatchUploadBCJob;
use App\Jobs\Uploader\BouncedCheque\UpdateBCDetailsJob;
use App\Models\Validator\BouncedCheque;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload BC data', function () {
    Queue::fake();

    BouncedCheque::factory()->count(3)->create([
        'client_number' => mt_rand(100, 900),
        'validationstatus' => 1,
    ]);

    (new BatchUploadBCJob)->handle();

    Queue::assertPushed(UpdateBCDetailsJob::class);
});

it('dispatches jobs to reload BC data', function () {
    Queue::fake();

    \App\Models\Validator\BouncedCheque::factory()->count(3)->create([
        'client_number' => mt_rand(100, 900),
        'validationstatus' => 1,
        'uploadstatus' => 3,
    ]);

    $job = new BatchUploadBCJob;
    $job->reload();
    $job->handle();

    Queue::assertPushed(UpdateBCDetailsJob::class);
});

it('adds job to BC batch when batch exists', function () {
    \App\Models\Validator\BouncedCheque::factory()->create([
        'validationstatus' => 1,
    ]);
    Queue::fake();

    [$job, $batch] = (new BatchUploadBCJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdateBCDetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});
