<?php

use App\Jobs\Uploader\CreditApplication\BatchInstitutionCAPDataJob;
use App\Jobs\Uploader\CreditApplication\BatchUploadCAPJob;
use App\Models\Validator\CreditApplication;
use App\Models\Validator\DataSubmission;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to batch upload CAP data', function () {
    Queue::fake();
    createTable('cap_cb005');
    $submissionRecord = [
        'file_identifier' => 'CAP',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    $details = CreditApplication::factory()
        ->make(['validationstatus' => 1, 'client_number' => mt_rand(600, 900)])
        ->toArray();
    \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('cap_cb005')
        ->insert($details);

    (new BatchUploadCAPJob)->handle();

    Queue::assertPushed(BatchInstitutionCAPDataJob::class);
});

it('dispatches jobs to reload batch upload for CAP data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CAP',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCAPJob)
        ->reload()
        ->handle();

    Queue::assertPushed(BatchInstitutionCAPDataJob::class, function (BatchInstitutionCAPDataJob $job) {
        return $job->uploadStatus === 3;
    });
});

it('adds job to CAP batch when batch exists', function () {
    $submissionRecord = [
        'file_identifier' => 'CAP',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    Queue::fake();

    [$job, $batch] = (new BatchUploadCAPJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(BatchInstitutionCAPDataJob::class);
    expect($batch)->totalJobs->toBe(1);
});
