<?php

use App\Jobs\Uploader\InstitutionBranch\BatchUploadIBJob;
use App\Jobs\Uploader\InstitutionBranch\UpdateIBDetailsJob;
use App\Models\Validator\InstitutionBranch;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload IB data', function () {
    Queue::fake();

    InstitutionBranch::factory()->create([
        'validationstatus' => 1,
    ]);

    (new BatchUploadIBJob)->handle();

    Queue::assertPushed(UpdateIBDetailsJob::class);
});

it('dispatches jobs to reload IB data', function () {
    Queue::fake();

    InstitutionBranch::factory()->create([
        'validationstatus' => 1,
        'uploadstatus' => 3,
    ]);

    $job = new BatchUploadIBJob;
    $job->reload();
    $job->handle();

    Queue::assertPushed(UpdateIBDetailsJob::class);
});

it('adds job to IB batch when batch exists', function () {
    $pi = \App\Models\Validator\InstitutionBranch::factory()->create([
        'validationstatus' => 1,
    ]);
    Queue::fake();

    [$job, $batch] = (new BatchUploadIBJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdateIBDetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});
