<?php

use App\Jobs\Uploader\ParticipatingInstitutionStakeholder\BatchUploadPISJob;
use App\Jobs\Uploader\ParticipatingInstitutionStakeholder\UpdatePISDetailsJob;
use App\Models\Validator\ParticipatingInstitutionStakeholder;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload PIS data', function () {
    Queue::fake();

    ParticipatingInstitutionStakeholder::factory()->create([
        'validationstatus' => 1,
    ]);

    (new BatchUploadPISJob)->handle();

    Queue::assertPushed(UpdatePISDetailsJob::class);
});

it('dispatches jobs to reload PIS data', function () {
    Queue::fake();

    ParticipatingInstitutionStakeholder::factory()->create([
        'validationstatus' => 1,
        'uploadstatus' => 3,
    ]);

    $job = new BatchUploadPISJob;
    $job->reload();
    $job->handle();

    Queue::assertPushed(UpdatePISDetailsJob::class);
});

it('adds job to PIS batch when batch exists', function () {
    $pi = \App\Models\Validator\ParticipatingInstitutionStakeholder::factory()->create([
        'validationstatus' => 1,
    ]);
    Queue::fake();

    [$job, $batch] = (new BatchUploadPISJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdatePISDetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});
