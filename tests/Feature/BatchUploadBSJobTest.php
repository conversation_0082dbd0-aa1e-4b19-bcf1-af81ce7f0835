<?php

use App\Jobs\Uploader\BorrowerStakeholder\BatchUploadBSJob;
use App\Jobs\Uploader\BorrowerStakeholder\UpdateBSDetailsJob;
use App\Models\Validator\BorrowerStakeholder;

it('dispatches jobs to upload BS data', function () {
    \Illuminate\Support\Facades\Queue::fake();

    BorrowerStakeholder::factory()->count(3)->create([
        'borrowers_client_number' => mt_rand(100, 900),
        'validationstatus' => 1,
    ]);

    (new BatchUploadBSJob)->handle();

    \Illuminate\Support\Facades\Queue::assertPushed(UpdateBSDetailsJob::class);
});

it('dispatches jobs to reload BS data', function () {
    \Illuminate\Support\Facades\Queue::fake();

    BorrowerStakeholder::factory()->count(3)->create([
        'borrowers_client_number' => mt_rand(100, 900),
        'validationstatus' => 1,
        'uploadstatus' => 3,
    ]);

    $job = new BatchUploadBSJob;
    $job->reload();
    $job->handle();

    \Illuminate\Support\Facades\Queue::assertPushed(UpdateBSDetailsJob::class);
});

it('adds job to BS batch when batch exists', function () {
    BorrowerStakeholder::factory()->create([
        'validationstatus' => 1,
    ]);
    Queue::fake();

    [$job, $batch] = (new BatchUploadBSJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdateBSDetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});
