<?php

use <PERSON><PERSON><PERSON>\MockInterface;

it('executes action to upload IB record', function () {
    $record = \App\Models\Validator\InstitutionBranch::factory()->create();
    $mock = $this->mock(\App\Actions\InstitutionBranch\UploadIBDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute')->once();
    });
    [$job, $batch] = (new \App\Jobs\Uploader\InstitutionBranch\UpdateIBDetailsJob($record->toArray()))->withFakeBatch();

    $job->handle($mock);

    $mock->shouldHaveReceived('execute');
});
