<?php

use App\Jobs\Uploader\CreditApplication\BatchInstitutionCAPDataJob;
use App\Jobs\Uploader\CreditApplication\UpdateCAPDetailsJob;
use App\Models\Validator\CreditApplication;
use Illuminate\Support\Facades\Queue;

it('does not dispatch batch without corresponding CAP table', function () {
    Queue::fake();

    [$job, $batch] = (new BatchInstitutionCAPDataJob('CB005', 0))->withFakeBatch();
    $job->handle();

    Queue::assertNotPushed(UpdateCAPDetailsJob::class);
});

it('adds to institution batch to upload CAP data', function () {
    Queue::fake();
    createTable('cap_cb005');

    $details = CreditApplication::factory()
        ->make([
            'validationstatus' => 1,
            'client_number' => mt_rand(600, 900),
            'uploadstatus' => 0,
        ])
        ->toArray();
    \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('cap_cb005')
        ->insert($details);

    [$job, $batch] = (new BatchInstitutionCAPDataJob('CB005', 0))->withFakeBatch();
    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdateCAPDetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});

it('dispatches job to upload CAP data', function () {
    Queue::fake();
    createTable('cap_cb005');

    $details = CreditApplication::factory()
        ->make([
            'validationstatus' => 1,
            'client_number' => mt_rand(600, 900),
            'uploadstatus' => 0,
        ])
        ->toArray();
    \Illuminate\Support\Facades\DB::connection('pgsql')
        ->table('cap_cb005')
        ->insert($details);

    $job = new BatchInstitutionCAPDataJob('CB005', 0);

    $job->handle();

    Queue::assertPushed(UpdateCAPDetailsJob::class);
});
