<?php

use App\Jobs\Uploader\ParticipatingInstitutions\BatchUploadPIJob;
use App\Jobs\Uploader\ParticipatingInstitutions\UpdatePIDetailsJob;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload PI data', function () {
    Queue::fake();

    \App\Models\Validator\ParticipatingInstitution::factory()->create([
        'validationstatus' => 1,
    ]);

    (new \App\Jobs\Uploader\ParticipatingInstitutions\BatchUploadPIJob)->handle();

    Queue::assertPushed(UpdatePIDetailsJob::class);
});

it('dispatches jobs to reload PI data', function () {
    Queue::fake();

    \App\Models\Validator\ParticipatingInstitution::factory()->create([
        'validationstatus' => 1,
        'uploadstatus' => 3,
    ]);

    $job = new \App\Jobs\Uploader\ParticipatingInstitutions\BatchUploadPIJob;
    $job->reload();
    $job->handle();

    Queue::assertPushed(UpdatePIDetailsJob::class);
});

it('adds job to batch when batch exists', function () {
    $pi = \App\Models\Validator\ParticipatingInstitution::factory()->create([
        'validationstatus' => 1,
    ]);
    Queue::fake();

    [$job, $batch] = (new BatchUploadPIJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(UpdatePIDetailsJob::class);
    expect($batch)->totalJobs->toBe(1);
});
