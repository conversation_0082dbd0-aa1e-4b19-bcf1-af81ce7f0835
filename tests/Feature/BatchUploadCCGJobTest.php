<?php

use App\Jobs\Uploader\CollateralCreditGuarantor\BatchInstitutionCCGDataJob;
use App\Jobs\Uploader\CollateralCreditGuarantor\BatchUploadCCGJob;
use App\Models\Validator\DataSubmission;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload CCG data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCCGJob)->handle();

    Queue::assertPushed(BatchInstitutionCCGDataJob::class);
});

it('dispatches jobs to reload CCG data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCCGJob)
        ->reload()
        ->handle();

    Queue::assertPushed(BatchInstitutionCCGDataJob::class, function (BatchInstitutionCCGDataJob $job) {
        return $job->uploadStatus === 3;
    });
});

it('adds job to CCG batch when batch exists', function () {
    $submissionRecord = [
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    Queue::fake();

    [$job, $batch] = (new BatchUploadCCGJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(BatchInstitutionCCGDataJob::class);
    expect($batch)->totalJobs->toBe(1);
});
