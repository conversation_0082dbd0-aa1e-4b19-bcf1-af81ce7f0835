<?php

use App\Actions\ParticipatingInstitutionStakeholders\UploadPISDetailsAction;
use App\Jobs\Uploader\ParticipatingInstitutionStakeholder\UpdatePISDetailsJob;
use App\Models\Validator\ParticipatingInstitutionStakeholder;
use Illuminate\Support\Facades\Queue;
use Mo<PERSON>y\MockInterface;

it('does not dispatch PIS job when batch is cancelled', function () {
    $pis = ParticipatingInstitutionStakeholder::factory()->create();

    Queue::fake();
    $mock = $this->mock(UploadPISDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('execute');
    });
    [$job, $batch] = (new UpdatePISDetailsJob($pis->toArray()))->withFakeBatch();

    $batch->cancel();
    $job->handle($mock);

    Queue::assertNotPushed(UpdatePISDetailsJob::class);
    expect($pis->refresh())
        ->uploadstatus
        ->toBe(0)
        ->and($mock)->shouldNotHaveReceived('execute');
});

it('executes action to upload PIS record', function () {
    $pi = ParticipatingInstitutionStakeholder::factory()->create();
    $mock = $this->mock(UploadPISDetailsAction::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute')->once();
    });
    [$job, $batch] = (new UpdatePISDetailsJob($pi->toArray()))->withFakeBatch();

    $job->handle($mock);

    $mock->shouldHaveReceived('execute');
});
