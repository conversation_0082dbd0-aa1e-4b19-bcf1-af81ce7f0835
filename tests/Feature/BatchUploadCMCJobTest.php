<?php

use App\Jobs\Uploader\CollateralMaterialCollateral\BatchInstitutionCMCDataJob;
use App\Jobs\Uploader\CollateralMaterialCollateral\BatchUploadCMCJob;
use App\Models\Validator\DataSubmission;
use Illuminate\Support\Facades\Queue;

it('dispatches jobs to upload CMC data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CMC',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCMCJob)->handle();

    Queue::assertPushed(BatchInstitutionCMCDataJob::class);
});

it('dispatches jobs to reload CMC data', function () {
    Queue::fake();

    $submissionRecord = [
        'file_identifier' => 'CMC',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    (new BatchUploadCMCJob)
        ->reload()
        ->handle();

    Queue::assertPushed(BatchInstitutionCMCDataJob::class, function (BatchInstitutionCMCDataJob $job) {
        return $job->uploadStatus === 3;
    });
});

it('adds job to CMC batch when batch exists', function () {
    $submissionRecord = [
        'file_identifier' => 'CMC',
        'pi_identification_code' => 'CB005',
    ];
    DataSubmission::factory()->create($submissionRecord);

    Queue::fake();

    [$job, $batch] = (new BatchUploadCMCJob)->withFakeBatch();

    $job->batchId = $batch->id;
    $job->handle();

    Queue::assertNotPushed(BatchInstitutionCMCDataJob::class);
    expect($batch)->totalJobs->toBe(1);
});
