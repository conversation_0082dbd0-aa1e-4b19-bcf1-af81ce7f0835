<?php

use Tests\TestCase;

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

uses(TestCase::class)->in('Feature', 'UI');
uses(\Tests\UploaderTestCase::class)->in('Uploader');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()
    ->extend('toBeOne', function () {
        return $this->toBe(1);
    });

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function validateErrors($errors, array $details): \Pest\Expectation
{
    [$controlCode, $enforcementCodes, $messages] = $details;

    return expect($errors)
        ->toBeArray()
        ->and(array_keys($errors))->toContain($controlCode)
        ->and(array_keys($errorMessages = array_first($errors)))->toContain($enforcementCodes)
        ->and($errorMessages)->toContain($messages);
}

function expectEnforcementToFail(array $errors, string $controlCode, string $enforcementCode, string $title)
{
    return expect($errors)->toBeArray()
        ->and(array_key_exists($controlCode, $errors))->toBeTrue()
        ->and(array_key_exists($enforcementCode, $enforcements = data_get($errors, $controlCode, [])))->toBeTrue()
        ->and(data_get($enforcements, $enforcementCode))->toContain($title);
}

function expectEnforcementToPass(array $errors, string $controlCode, string|array $enforcementCode)
{
    $expectation = expect($errors)->toBeArray();

    if (is_string($enforcementCode)) {
        return $expectation->and(array_key_exists($enforcementCode, data_get($errors, $controlCode, [])))->toBeFalse();
    }

    return $expectation->and(in_array($enforcementCode, array_keys(data_get($errors, $controlCode, []))));
}

function createTable(string $table, string $fileType = ''): void
{
    $fileType = str($table)->contains('_') ? str($table)->before('_')->upper()->toString() : '';

    if (empty($fileType)) {
        return;
    }

    switch ($fileType) {
        case 'CAP':
            create_cap($table);
            break;
        case 'CBA':
            create_cba($table);
            break;
        case 'CCG':
            create_ccg($table);
            break;
        case 'CMC':
            create_cmc($table);
            break;
        default:
            break;
    }
}

function insertCreatedTable(string $table, array $records): int
{
    return \Illuminate\Support\Facades\DB::table($table)->insertGetId($records);
}

function findRecord($id, string $table_name)
{
    return \Illuminate\Support\Facades\DB::table($table_name)->find($id);
}
