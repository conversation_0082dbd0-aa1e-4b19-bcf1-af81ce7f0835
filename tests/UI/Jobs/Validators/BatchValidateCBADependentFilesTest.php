<?php

it('batches jobs to validate cba dependent files', function () {
    Bus::fake();

    $job = new \App\Jobs\UI\Validators\BatchValidateCBADependantFilesJob('CB005', [
        'CB00520231031CCG.csv',
        'CB00520231031CMC.csv',
        'CB00520231031BS.csv',
    ]);

    $job->handle();

    Bus::assertBatched(function (\Illuminate\Bus\PendingBatch $batch) {
        return $batch->jobs->count() === 3
            && $batch->name = 'Batch Validate CB005'
            && $batch->connection() === 'validation'
            && $batch->queue() === 'broadcast'
            && $batch->allowsFailures();
    });
});

it('does not batch jobs to validate non cba dependent files', function () {
    Bus::fake();

    $job = new \App\Jobs\UI\Validators\BatchValidateCBADependantFilesJob('CB005', [
        'CB00520231031PI.csv',
        'CB00520231031CBA.csv',
    ]);

    $job->handle();

    Bus::assertNothingBatched();
});
