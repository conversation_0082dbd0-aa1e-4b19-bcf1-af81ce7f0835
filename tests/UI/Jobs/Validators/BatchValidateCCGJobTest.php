<?php

use App\Models\Validator\DataSubmission;
use App\Services\ValidateDataService;
use Mo<PERSON>y\MockInterface;

it('does not run CCG validation when batch is canceled', function () {
    Queue::fake();

    $mock = $this->mock(ValidateDataService::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('use', 'run');
    });

    [$job, $batch] = (new \App\Jobs\UI\Validators\BatchValidateCCGJob('CB005', '20231031'))->withFakeBatch();

    $batch->cancel();
    $job->handle();

    $mock->shouldNotHaveReceived('use');
    $mock->shouldNotHaveReceived('run');
});

it('requires a CCG submission to validate', function () {
    createTable('ccg_cb005', 'CCG');

    (new \App\Jobs\UI\Validators\BatchValidateCCGJob('CB005', '20231031'))->handle();

    $this->assertDatabaseEmpty('ccg_cb005');
});

it('requires CCG table', function () {
    $submissionRecord = [
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
    ];
    $submission = DataSubmission::factory()->create($submissionRecord);

    // Creates a valid PI record expected to pass all checks
    (new \App\Jobs\UI\Validators\BatchValidateCCGJob('CB005', '20231031'))->handle();

    expect($submission->refresh())->validationstatus->toBe(0);
});

it('runs validation service for CCG', function () {
    createTable('ccg_cb005', 'CCG');

    $submissionRecord = [
        'file_identifier' => 'CCG',
        'pi_identification_code' => 'CB005',
        'submission_date' => '20231031',
    ];
    $submission = DataSubmission::factory()->create($submissionRecord);

    $mock = $this->mock(ValidateDataService::class, function (MockInterface $mock) {
        $mock->shouldReceive('use')->andReturnSelf();
        $mock->shouldReceive('run')->once();
    });

    [$job, $batch] = (new \App\Jobs\UI\Validators\BatchValidateCCGJob($submission->pi_identification_code, $submission->submission_date))
        ->withFakeBatch();

    $job->handle();

    expect($mock)->shouldHaveReceived('use')
        ->and($mock)->shouldHaveReceived('run');
});
