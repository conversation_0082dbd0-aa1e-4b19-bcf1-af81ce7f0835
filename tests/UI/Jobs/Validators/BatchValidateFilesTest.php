<?php

it('dispatches jobs to validate files', function () {
    Bus::fake();

    $job = new \App\Jobs\UI\Validators\BatchValidateFilesJob('CB005', [
        'CB00520231031BC.csv',
        'CB00520231031PI.csv',
        'CB00520231031PIS.csv',
    ]);

    $job->handle();

    Bus::assertBatched(function (\Illuminate\Bus\PendingBatch $batch) {
        return $batch->jobs->count() === 3
            && $batch->name = 'Batch Validate CB005'
            && $batch->connection() === 'validation'
            && $batch->queue() === 'broadcast';
    });
});

it('does not batch jobs dependent on CBA', function () {
    Bus::fake();

    $job = new \App\Jobs\UI\Validators\BatchValidateFilesJob('CB005', [
        'CB00520231031BC.csv',
        'CB00520231031PI.csv',
        'CB00520231031PIS.csv',
        'CB00520231031CBA.csv',
        'CB00520231031BS.csv', // Depends on CBA
        'CB00520231031CCG.csv', // Depends on CBA
    ]);

    $job->handle();

    Bus::assertBatched(function (\Illuminate\Bus\PendingBatch $batch) {
        return $batch->jobs->count() === 4
            && $batch->name = 'Batch Validate CB005'
                && $batch->connection() === 'validation'
                && $batch->queue() === 'broadcast';
    });
});
