<?php

use App\Jobs\UI\Importer\BatchImportCSVJob;
use App\Livewire\MultiFileUploader;
use App\Models\User;
use App\Models\Validator\DataSubmission;
use App\Notifications\FilesSubmitted;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;

it('renders successfully', function () {
    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->assertStatus(200)
        ->assertViewIs('livewire.multi-file-uploader')
        ->assertsee(['This is a Test Submission', 'This is our Final Submission']);
});

it('can upload a single file', function () {
    $contents = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520231031BC.csv', $contents);

    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    expect($livewire->get('uploadedFiles'))
        ->toHaveCount(1)
        ->toContain('CB00520231031BC.csv');
});

it('can upload multiple files', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    $bsContent = Storage::disk('local')->get('testing/CB005/CB00520231031BS.csv');

    Storage::fake('local');
    $file1 = UploadedFile::fake()->createWithContent('CB00520231031BC.csv', $bcContent);
    $file2 = UploadedFile::fake()->createWithContent('CB00520231031BS.csv', $bsContent);
    $files = [$file1, $file2];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    expect($livewire->get('uploadedFiles'))
        ->toHaveCount(2)
        ->toContain('CB00520231031BC.csv', 'CB00520231031BS.csv');
});

it('cannot upload non CSV file', function ($mimeTypeNotAllowed) {
    Storage::fake('local');

    $file = UploadedFile::fake()->create('CB00520231031BC.pdf', 1000, $mimeTypeNotAllowed);
    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire->assertHasErrors('files.0');
    expect($livewire->get('uploadedFiles'))->toBeEmpty();
})->with(mimeTypes());

// The uploaded files must be in the list (PI, IB, PIS, FRA, BC, BS, CAP, CBA, CMC, CCG)
it('cannot upload CSV with invalid filename', function ($invalidName) {
    Storage::fake('local');

    $file = UploadedFile::fake()->create($invalidName, 100, 'text/csv');

    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
})->with([
    'CB00520231031.csv', // File does not end with known File Type
    'CB005.csv', // File starts with PI Code but does not have submission date or file type
    '20231031CB005BC.csv',
    'BC20231031CB005.csv',
]);

it('cannot upload CSV with a non-matching pi code', function () {
    Storage::fake('local');

    $file = UploadedFile::fake()->create('CB00520231031BC.csv', 100, 'text/csv');

    $user = User::factory()->create(['pi_code' => 'CB004']);
    $livewire = Livewire::actingAs($user)
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();

    $user->pi_code = null;
    $user->save();
    $livewire = Livewire::test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB004')
        ->set('files', [$file])
        ->call('uploadFiles');

    expect(array_flatten($livewire->errors()->messages()))
        ->toContain("CB00520231031BC.csv's filename must start with: CB004");
});

it('cannot upload with invalid submission date in filename', function ($filenameWithInvalidSubmissionDate) {
    Storage::fake('local');

    $file = UploadedFile::fake()->create($filenameWithInvalidSubmissionDate, 100, 'text/csv');
    $user = User::factory()->create(['pi_code' => 'CB005']);

    $livewire = Livewire::actingAs($user)
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();

    expect(array_flatten($livewire->errors()->messages()))
        ->toContain($filenameWithInvalidSubmissionDate.' must have a valid submission date after the PI Code.');
})->with([
    'CB0052O231031BC.csv',
    'CB00520231O31BC.csv',
]);

it('cannot upload a file with an invalid file type', function () {
    Storage::fake('local');

    $file = UploadedFile::fake()->create('CB00520231031BZ.csv', 100, 'text/csv');
    $user = User::factory()->create(['pi_code' => 'CB005']);

    $livewire = Livewire::actingAs($user)
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();

    expect(array_flatten($livewire->errors()->messages()))
        ->toContain('CB00520231031BZ.csv must have a valid file type. Allowed file types are: '.implode(', ', array_keys(config('uploader.files'))));
});

it('flags header with more than seven columns', function () {
    $contents = Storage::disk('local')->get('testing/CB005/CB00520230930BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520230930BC.csv', $contents);

    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
    expect(array_flatten($livewire->errors()->messages()))
        ->toContain("CB00520230930BC.csv's header record is invalid.");
});

it('flags header not starting with H as first character', function () {
    $contents = Storage::disk('local')->get('testing/CB005/CB00520230831BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520230831BC.csv', $contents);

    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
    expect(array_flatten($livewire->errors()->messages()))
        ->toContain("CB00520230831BC.csv's header record must start with 'H' as first value.");
});

it('flags mismatching pi codes between header and filename', function () {
    $contents = Storage::disk('local')->get('testing/CB005/CB00520230731BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520230731BC.csv', $contents);

    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
    expect(array_flatten($livewire->errors()->messages()))
        ->toContain("The institution code in CB00520230731BC.csv's header record does not match the code in the filename.");
});

it('flags mismatching file types between header and filename', function () {
    $contents = Storage::disk('local')->get('testing/CB005/CB00520230630BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520230630BC.csv', $contents);

    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
    expect(array_flatten($livewire->errors()->messages()))
        ->toContain("The file identifier in CB00520230630BC.csv's header record does not match the file identifier in the filename.");
});

it('flags mismatching submission date between header and filename', function () {
    $contents = Storage::disk('local')->get('testing/CB005/CB00520230531BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520230531BC.csv', $contents);

    $files = [$file];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
    expect(array_flatten($livewire->errors()->messages()))
        ->toContain("The submission date in CB00520230531BC.csv's header record does not match the submission date in the filename.");
});

it('flags when file is not uploaded', function () {

    $files = ['some text'];
    $livewire = Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', $files)
        ->call('uploadFiles');

    $livewire
        ->assertCount('uploadedFiles', 0)
        ->assertHasErrors();
    expect(array_flatten($livewire->errors()->messages()))
        ->toContain('The files.0 must be an uploaded file');
});

it('dispatches post reset event', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');

    $file = UploadedFile::fake()
        ->createWithContent('CB00520231031BC.csv', $bcContent);

    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors()
        ->assertDispatched('pond-reset');
});

it('dispatches job to process uploaded files', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');
    Queue::fake();

    $file = UploadedFile::fake()
        ->createWithContent('CB00520231031BC.csv', $bcContent);

    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors();

    Queue::assertPushedOn('broadcast', BatchImportCSVJob::class);
});

it('sends notification to admins when submission is final', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');
    Notification::fake();

    $file = UploadedFile::fake()
        ->createWithContent('CB00520231031BC.csv', $bcContent);
    $adminEmails = explode(',', config('uploader.admins'));

    foreach ($adminEmails as $email) {
        User::factory()->create(['email' => $email]);
    }

    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'final')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors();

    $users = User::query()->whereIn('email', $adminEmails)->get();

    Notification::assertSentTo($users, function (FilesSubmitted $notification) use ($users) {
        return $notification->toMail($users)->greeting === 'Hello Admin' && $notification->toArray($users) === [];
    });
});

it('does not send notification when admins do not exist', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');
    Notification::fake();

    $file = UploadedFile::fake()
        ->createWithContent('CB00520231031BC.csv', $bcContent);

    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'final')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors();

    $adminEmails = explode(',', config('uploader.admins'));
    $adminCount = User::query()->whereIn('email', $adminEmails)->count();

    expect($adminCount)->toEqual(0);

    Notification::assertNothingSent();
});

it('does not send notification to admins when submission is test', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');
    Notification::fake();

    $file = UploadedFile::fake()
        ->createWithContent('CB00520231031BC.csv', $bcContent);
    $adminEmails = explode(',', config('uploader.admins'));

    foreach ($adminEmails as $email) {
        User::factory()->create(['email' => $email]);
    }

    Livewire::actingAs(User::factory()->create())
        ->test(MultiFileUploader::class, ['piCode' => 'CB005'])
        ->set('submissionType', 'test')
        ->set('piCode', 'CB005')
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors();

    $users = User::query()->whereIn('email', $adminEmails)->get();

    Notification::assertNotSentTo($users, FilesSubmitted::class);
});

it('marks submission as re-submitted', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');

    $submission = DataSubmission::factory()
        ->create([
            'pi_identification_code' => 'CB005',
            'validationstatus' => 1,
            'file_identifier' => 'BC',
        ]);

    $user = User::factory()->create(['pi_code' => $submission->pi_identification_code]);
    $filename = $submission->pi_identification_code.'20231031'.$submission->file_identifier.'.csv';
    $file = UploadedFile::fake()
        ->createWithContent($filename, $bcContent);

    Livewire::actingAs($user)
        ->test(MultiFileUploader::class, ['piCode' => $user->pi_code])
        ->set('submissionType', 'resubmission')
        ->set('piCode', $user->pi_code)
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors();

    $submission->refresh();

    expect($submission)
        ->resubmitted_at->not->toBeNull();
});

it('doest not mark submission as re-submitted when validation not completed', function () {
    $bcContent = Storage::disk('local')->get('testing/CB005/CB00520231031BC.csv');
    Storage::fake('local');

    $submission = DataSubmission::factory()
        ->create([
            'pi_identification_code' => 'CB005',
            'file_identifier' => 'BC',
        ]);

    $user = User::factory()->create(['pi_code' => $submission->pi_identification_code]);
    $filename = $submission->pi_identification_code.'20231031'.$submission->file_identifier.'.csv';
    $file = UploadedFile::fake()
        ->createWithContent($filename, $bcContent);

    Livewire::actingAs($user)
        ->test(MultiFileUploader::class, ['piCode' => $user->pi_code])
        ->set('submissionType', 'final')
        ->set('piCode', $user->pi_code)
        ->set('files', [$file])
        ->call('uploadFiles')
        ->assertCount('uploadedFiles', 1)
        ->assertHasNoErrors();

    $submission->refresh();

    expect($submission)
        ->resubmitted_at->toBeNull();
});

function mimeTypes(): array
{
    return [
        'application/octet-stream', // .bin files
        'application/pdf', // .pdf
        'application/x-httpd-php', // .php
        'application/x-sh', // .sh - Bourne shell script
        'application/zip', // .zip
        'image/bmp', // .bmp
        'image/png', // .png
        'image/svg+xml', // .svg
        'application/msword', // .doc - Microsoft Word
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx - Microsoft Word (OpenXML)
        'text/html', // .htm, .html - HTML files
        'text/javascript', // .js - JavaScript
        'audio/mpeg', // .mp3
        'video/mp4', // .mp4
    ];
}
