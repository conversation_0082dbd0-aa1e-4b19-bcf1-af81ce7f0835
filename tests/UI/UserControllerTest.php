<?php

it('renders users page', function () {
    $adminEmail = array_last(explode(',', config('uploader.admins')));
    $admin = \App\Models\User::factory()->create([
        'email' => $adminEmail,
    ]);
    $users = \App\Models\User::factory()->count(3)->create();

    $response = $this->actingAs($admin)
        ->get(route('admin.users.index'));

    $response
        ->assertOk()
        ->assertViewIs('admin.users.index')
        ->assertViewHasAll(['users', 'institutions'])
        ->assertSee('Users')
        ->assertSee($admin->name); // Logged in user

    foreach ($users as $user) {
        $response->assertSee($user->name);
    }
});

it('renders page to create users', function () {
    $adminEmail = array_last(explode(',', config('uploader.admins')));
    $admin = \App\Models\User::factory()->create([
        'email' => $adminEmail,
    ]);

    $response = $this->actingAs($admin)
        ->get(route('admin.users.create'));

    $response->assertOk()
        ->assertViewIs('admin.users.create')
        ->assertSeeLivewire(\App\Livewire\Admin\CreateUser::class);
});
