<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

/**
 * @codeCoverageIgnoreStart
 */
class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array<string>
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command('uploader:run-data-uploader')->daily()->withoutOverlapping();
        $schedule->command('reloader:run-data-reloader')->hourly()->withoutOverlapping();
        $schedule->command('crb:generate-individual-score')->everyMinute();
        $schedule->command('dwh:aggregate-loan-account-statistics')->daily();
        $schedule->command('dwh:aggregate-loan-application-statistics')->daily();
        // Prunes stale cache tag entries
        // $schedule->command('cache:prune-stale-tags')->hourly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
/**
 * @codeCoverageIgnoreEnd
 */
