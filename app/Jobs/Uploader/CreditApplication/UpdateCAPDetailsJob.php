<?php

namespace App\Jobs\Uploader\CreditApplication;

use App\Actions\CreditApplication\UploadCADetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class UpdateCAPDetailsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Collection<int, object>
     */
    protected Collection $credit_applications;

    protected string $table_name;

    /**
     * Create a new job instance.
     *
     * @param  Collection<int, object>  $credit_applications
     * @return void
     */
    public function __construct(Collection $credit_applications, string $table_name)
    {
        $this->credit_applications = $credit_applications;
        $this->table_name = $table_name;
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(UploadCADetailsAction $action): void
    {
        if ($this->batch()?->canceled()) {
            return;
        }

        $action->execute($this->credit_applications, $this->table_name);
    }
}
