<?php

namespace App\Jobs\Uploader\CreditApplication;

use App\Actions\CreditApplication\UploadOnlineCADetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class UploadOnlineCreditApplicationJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Collection<int, object>
     */
    protected Collection $applications;

    /**
     * Create a new job instance.
     *
     * @param  Collection<int, object>  $applications
     * @return void
     */
    public function __construct(Collection $applications)
    {
        $this->applications = $applications;
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(UploadOnlineCADetailsAction $action): void
    {
        if ($this->batch()?->canceled()) {
            return;
        }

        $action->execute($this->applications);
    }
}
