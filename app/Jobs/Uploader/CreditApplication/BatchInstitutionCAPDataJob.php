<?php

namespace App\Jobs\Uploader\CreditApplication;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class BatchInstitutionCAPDataJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $recordChunkSize = 500;

    protected string $tableName = '';

    public int $uploadStatus = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $pi, int $uploadStatus)
    {
        $this->tableName = strtolower("cap_$pi");
        $this->uploadStatus = $uploadStatus;
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        if (! Schema::connection('pgsql')->hasTable($this->tableName)) {
            logger()->warning("Table $this->tableName does not exist");

            return;
        }

        DB::connection('pgsql')
            ->table($this->tableName)
            ->where([
                'validationstatus' => 1,
                'uploadstatus' => $this->uploadStatus,
            ])
            ->orderBy('submission_date')
            ->chunk(
                $this->recordChunkSize,
                fn (Collection $creditApplications) => $this->dispatchJob($creditApplications)
            );
    }

    /**
     * @param  Collection<int, object>  $creditApplications
     */
    protected function dispatchJob(Collection $creditApplications): void
    {
        $job = new UpdateCAPDetailsJob($creditApplications, $this->tableName);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }
}
