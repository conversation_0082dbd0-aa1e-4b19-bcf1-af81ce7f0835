<?php

namespace App\Jobs\Uploader;

use App\Models\Uploader\prod\Individual;
use App\Models\Uploader\prod\NonIndividual;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

/**
 * @codeCoverageIgnoreStart
 */
class AttachCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var array<string, string|int|null>
     */
    private array $data;

    private int $borrower_classification;

    /**
     * Create a new job instance.
     *
     * @param  array<string, string|int|null>  $data
     * @return void
     */
    public function __construct(array $data, int $borrower_classification)
    {
        $this->data = $data;
        $this->borrower_classification = $borrower_classification;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->borrower_classification == 0) {
            $this->attachIndividual();
        } else {
            $this->attachNonIndividual();
        }
    }

    private function attachIndividual(): void
    {
        $builder = Individual::individualIdentifierBuilder($this->data);
        $identifier = $builder->first();

        if (is_null($identifier)) {
            $individual = Individual::createIndividualInformation($this->data);

            // Then attach the individual id to cba
            DB::connection('mysql')->table('credit_borrower_account')->where([
                'pi_identification_code' => $this->data['pi_identification_code'],
                'branch_identification_code' => $this->data['branch_identification_code'],
                'borrowers_client_number' => $this->data['borrowers_client_number'],
                'credit_account_reference' => $this->data['credit_account_reference'],
            ])->update(['individual_id' => $individual->id]);
        } else {
            Individual::updateIndividualInformation($this->data, $identifier->individual_id);

            DB::connection('mysql')->table('credit_borrower_account')->where([
                'pi_identification_code' => $this->data['pi_identification_code'],
                'branch_identification_code' => $this->data['branch_identification_code'],
                'borrowers_client_number' => $this->data['borrowers_client_number'],
                'credit_account_reference' => $this->data['credit_account_reference'],
            ])->update(['individual_id' => $identifier->individual_id]);
        }
    }

    private function attachNonIndividual(): void
    {
        $builder = NonIndividual::individualIdentifierBuilder($this->data);
        $identifier = $builder->first();

        if ($identifier == null) {
            $non_individual = NonIndividual::createNonIndividualInformation($this->data);

            // attach the non-individual to the cba
            DB::connection('mysql')->table('credit_borrower_account')->where([
                'pi_identification_code' => $this->data['pi_identification_code'],
                'branch_identification_code' => $this->data['branch_identification_code'],
                'borrowers_client_number' => $this->data['borrowers_client_number'],
                'credit_account_reference' => $this->data['credit_account_reference'],
            ])->update(['nonindividual_id' => $non_individual->id]);
        } else {
            NonIndividual::updateNonIndividualInformation($this->data, $identifier->non_individual_id);

            DB::connection('mysql')->table('credit_borrower_account')->where([
                'pi_identification_code' => $this->data['pi_identification_code'],
                'branch_identification_code' => $this->data['branch_identification_code'],
                'borrowers_client_number' => $this->data['borrowers_client_number'],
                'credit_account_reference' => $this->data['credit_account_reference'],
            ])->update(['nonindividual_id' => $identifier->non_individual_id]);
        }
    }
}
/**
 * @codeCoverageIgnoreEnd
 */
