<?php

namespace App\Jobs\Uploader\BouncedCheque;

use App\Jobs\ShouldReload;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BatchUploadBCJob implements ShouldQueue, ShouldReload
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $recordChunkSize = 500;

    protected int $uploadStatus = 0;

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        DB::connection('pgsql')
            ->table('bounced_cheques')
            ->where(['validationstatus' => 1, 'uploadstatus' => $this->uploadStatus])
            ->orderBy('submission_date')
            ->pluck('id')
            ->chunk($this->recordChunkSize)
            ->each(fn (Collection $chequeIds) => $this->dispatchJob($chequeIds));
    }

    /**
     * @param  Collection<int, object>  $chequeIds
     */
    protected function dispatchJob(Collection $chequeIds): void
    {
        $job = new UpdateBCDetailsJob($chequeIds);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }

    public function reload(): BatchUploadBCJob
    {
        $this->uploadStatus = 3;

        return $this;
    }
}
