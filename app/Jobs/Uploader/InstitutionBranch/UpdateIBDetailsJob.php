<?php

namespace App\Jobs\Uploader\InstitutionBranch;

use App\Actions\InstitutionBranch\UploadIBDetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateIBDetailsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var array<string>
     */
    protected array $branch;

    /**
     * Create a new job instance.
     *
     * @param  array<string>  $branch
     * @return void
     */
    public function __construct(array $branch)
    {
        $this->branch = $branch;
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(UploadIBDetailsAction $action): void
    {
        $action->execute($this->branch);
    }
}
