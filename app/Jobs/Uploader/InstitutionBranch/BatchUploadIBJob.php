<?php

namespace App\Jobs\Uploader\InstitutionBranch;

use App\Jobs\ShouldReload;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class BatchUploadIBJob implements ShouldQueue, ShouldReload
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $uploadStatus = 0;

    protected int $recordChunkSize = 500;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        DB::connection('pgsql')
            ->table('institution_branch')
            ->where([
                'validationstatus' => 1,
                'uploadstatus' => $this->uploadStatus,
            ])
            ->orderBy('submission_date')
            ->each(fn ($branch) => $this->dispatchJob((array) $branch));
    }

    /**
     * @param  array<string>  $branch
     */
    protected function dispatchJob(array $branch): void
    {
        $job = new UpdateIBDetailsJob($branch);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }

    public function reload(): BatchUploadIBJob
    {
        $this->uploadStatus = 3;

        return $this;
    }
}
