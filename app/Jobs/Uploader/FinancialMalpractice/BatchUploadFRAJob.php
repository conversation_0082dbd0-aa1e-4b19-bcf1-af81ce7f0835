<?php

namespace App\Jobs\Uploader\FinancialMalpractice;

use App\Jobs\ShouldReload;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BatchUploadFRAJob implements ShouldQueue, ShouldReload
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public int $batchChunkSize = 10;

    public int $recordChunkSize = 100;

    public int $uploadStatus = 0;

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        DB::connection('pgsql')
            ->table('financial_malpractice_data')
            ->where(['validationstatus' => 1, 'uploadstatus' => $this->uploadStatus])
            ->orderBy('submission_date')
            ->chunk(
                $this->recordChunkSize,
                fn (Collection $malpractices) => $this->dispatchJob($malpractices)
            );
    }

    /**
     * @param  Collection<int, object>  $malpractices
     */
    protected function dispatchJob(Collection $malpractices): void
    {
        $job = new UpdateFRADetailsJob($malpractices);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }

    public function reload(): BatchUploadFRAJob
    {
        $this->uploadStatus = 3;

        return $this;
    }
}
