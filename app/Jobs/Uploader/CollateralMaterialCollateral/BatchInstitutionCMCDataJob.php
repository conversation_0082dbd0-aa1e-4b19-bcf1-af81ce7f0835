<?php

namespace App\Jobs\Uploader\CollateralMaterialCollateral;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class BatchInstitutionCMCDataJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $recordChunkSize = 500;

    public int $uploadStatus = 0;

    protected string $tableName;

    public function __construct(string $pi, int $uploadStatus)
    {
        $this->tableName = strtolower("cmc_$pi");
        $this->uploadStatus = $uploadStatus;
    }

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        if (! Schema::connection('pgsql')->hasTable($this->tableName)) {
            Log::debug("Table $this->tableName does not exist");

            return;
        }

        DB::connection('pgsql')
            ->table($this->tableName)
            ->select('id')
            ->where(['validationstatus' => 1, 'uploadstatus' => $this->uploadStatus])
            ->orderBy('submission_date')
            ->pluck('id')
            ->chunk($this->recordChunkSize)
            ->each(fn (Collection $collateralIds) => $this->dispatchJob($collateralIds));
    }

    /**
     * @param  Collection<int, object>  $collateralIds
     */
    protected function dispatchJob(Collection $collateralIds): void
    {
        $job = new UpdateCMCDetailsJob($collateralIds, $this->tableName);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }
}
