<?php

namespace App\Jobs\Uploader\CollateralMaterialCollateral;

use App\Actions\CollateralMaterialCollateral\UploadCMCDetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class UpdateCMCDetailsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Collection<int, object>
     */
    protected Collection $collaterals;

    protected string $tableName;

    /**
     * @param  Collection<int, object>  $collaterals
     */
    public function __construct(Collection $collaterals, string $tableName)
    {
        $this->collaterals = $collaterals;
        $this->tableName = $tableName;
    }

    /**
     * @throws \Throwable
     */
    public function handle(UploadCMCDetailsAction $action): void
    {
        $action->execute($this->collaterals->toArray(), $this->tableName);
    }
}
