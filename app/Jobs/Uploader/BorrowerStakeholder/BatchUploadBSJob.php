<?php

namespace App\Jobs\Uploader\BorrowerStakeholder;

use App\Jobs\ShouldReload;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BatchUploadBSJob implements ShouldQueue, ShouldReload
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $recordChunkSize = 500;

    protected int $uploadStatus = 0;

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        DB::connection('pgsql')
            ->table('borrower_stakeholders')
            ->where(['validationstatus' => 1, 'uploadstatus' => $this->uploadStatus])
            ->orderBy('submission_date')
            ->pluck('id')
            ->chunk($this->recordChunkSize)
            ->each(fn (Collection $chunkedIds) => $this->dispatchJob($chunkedIds));
    }

    /**
     * @param  Collection<int, object>  $ids
     */
    protected function dispatchJob(Collection $ids): void
    {
        $job = new UpdateBSDetailsJob($ids);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }

    public function reload(): BatchUploadBSJob
    {
        $this->uploadStatus = 3;

        return $this;
    }
}
