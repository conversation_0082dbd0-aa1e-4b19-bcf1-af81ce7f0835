<?php

namespace App\Jobs\Uploader\CreditBorrowerAccount;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class BatchInstitutionCBADataJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $recordChunkSize = 500;

    public int $uploadStatus = 0;

    protected string $tableName;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $pi, int $uploadStatus)
    {
        $this->tableName = strtolower("cba_$pi");
        $this->uploadStatus = $uploadStatus;
    }

    /**
     * Execute the job.
     *
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        if (! Schema::connection('pgsql')->hasTable($this->tableName)) {
            logger()->warning("Table $this->tableName does not exist");

            return;
        }

        DB::connection('pgsql')
            ->table($this->tableName)
            ->where(['validationstatus' => 1, 'uploadstatus' => $this->uploadStatus])
            ->orderBy('submission_date')
            ->pluck('id')
            ->chunk($this->recordChunkSize)
            ->each(fn (Collection $cbaIds) => $this->dispatchJob($cbaIds));
    }

    /**
     * @param  Collection<int, int>  $cbaIds
     */
    protected function dispatchJob(Collection $cbaIds): void
    {
        $job = new UpdateCBADetailsJob($cbaIds, $this->tableName);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }
}
