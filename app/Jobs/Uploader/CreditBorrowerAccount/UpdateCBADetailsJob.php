<?php

namespace App\Jobs\Uploader\CreditBorrowerAccount;

use App\Actions\CreditBorrowerAccount\UploadCBADetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class UpdateCBADetailsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Collection<int, int>
     */
    protected Collection $creditBorrowerAccountIds;

    protected string $tableName;

    /**
     * @param  Collection<int, int>  $accountIds
     */
    public function __construct(Collection $accountIds, string $tableName)
    {
        $this->creditBorrowerAccountIds = $accountIds;
        $this->tableName = $tableName;
    }

    /**
     * @throws \Throwable
     */
    public function handle(UploadCBADetailsAction $action): void
    {
        if ($this->batch()?->canceled()) {
            return;
        }

        $action->execute($this->creditBorrowerAccountIds->all(), $this->tableName);
    }
}
