<?php

namespace App\Jobs\Uploader\CollateralCreditGuarantor;

use App\Actions\CCG\UploadCCGDetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class UpdateCCGDetailsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Collection<int, object>
     */
    protected Collection $guarantorIds;

    protected string $tableName;

    /**
     * @param  Collection<int, object>  $guarantorIds
     */
    public function __construct(Collection $guarantorIds, string $tableName)
    {
        $this->guarantorIds = $guarantorIds;
        $this->tableName = $tableName;
    }

    /**
     * @throws \Throwable
     */
    public function handle(UploadCCGDetailsAction $action): void
    {
        if ($this->batch()?->canceled()) {
            return;
        }

        $action->execute($this->guarantorIds->toArray(), $this->tableName);
    }
}
