<?php

namespace App\Jobs\Uploader\CollateralCreditGuarantor;

use App\Jobs\ShouldReload;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class BatchUploadCCGJob implements ShouldQueue, ShouldReload
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $uploadStatus = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        DB::connection('pgsql')
            ->table('datasubmissions')
            ->distinct('pi_identification_code')
            ->where('file_identifier', 'CCG')
            ->pluck('pi_identification_code')
            ->each(fn ($pi) => $this->dispatchJob($pi));
    }

    protected function dispatchJob(string $pi): void
    {
        $job = new BatchInstitutionCCGDataJob($pi, $this->uploadStatus);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }

    public function reload(): BatchUploadCCGJob
    {
        $this->uploadStatus = 3;

        return $this;
    }
}
