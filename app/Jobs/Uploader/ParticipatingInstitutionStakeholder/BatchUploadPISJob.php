<?php

namespace App\Jobs\Uploader\ParticipatingInstitutionStakeholder;

use App\Jobs\ShouldReload;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class BatchUploadPISJob implements ShouldQueue, ShouldReload
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $uploadStatus = 0;

    protected int $recordChunkSize = 500;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        DB::connection('pgsql')
            ->table('pi_stakeholders')
            ->where([
                'validationstatus' => 1,
                'uploadstatus' => $this->uploadStatus,
            ])
            ->orderBy('submission_date')
            ->each(fn ($stakeholder) => $this->dispatchJob((array) $stakeholder));
    }

    /**
     * @param  array<string>  $stakeholder
     */
    protected function dispatchJob(array $stakeholder): void
    {
        $job = new UpdatePISDetailsJob($stakeholder);

        if ($this->batchId) {
            $this->batch()?->add($job);

            return;
        }

        dispatch($job);
    }

    public function reload(): BatchUploadPISJob
    {
        $this->uploadStatus = 3;

        return $this;
    }
}
