<?php

namespace App\Jobs\Uploader\ParticipatingInstitutionStakeholder;

use App\Actions\ParticipatingInstitutionStakeholders\UploadPISDetailsAction;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdatePISDetailsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var array<string>
     */
    protected array $stakeholder;

    /**
     * Create a new job instance.
     *
     * @param  array<string>  $stakeholder
     * @return void
     */
    public function __construct(array $stakeholder)
    {
        $this->stakeholder = $stakeholder;
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(UploadPISDetailsAction $action): void
    {
        // Check if the batch is still active before running any more job code
        if ($this->batch()?->canceled()) {
            return;
        }

        $action->execute($this->stakeholder);
    }
}
