<?php

use App\Models\Validator\BorrowerStakeholder;
use App\Models\Validator\BouncedCheque;
use App\Models\Validator\CollateralCreditGuarantor;
use App\Models\Validator\CollateralMaterialCollateral;
use App\Models\Validator\CreditApplication;
use App\Models\Validator\CreditBorrowerAccount;
use App\Models\Validator\CreditScore;
use App\Models\Validator\FinancialMalpracticeData;
use App\Models\Validator\InstitutionBranch;
use App\Models\Validator\ParticipatingInstitution;
use App\Models\Validator\ParticipatingInstitutionStakeholder;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;

if (! function_exists('create_cba')) {
    function create_cba(string $table_name): void
    {
        Schema::create($table_name, function (Blueprint $table) {
            $table->increments('id');
            $table->string('submission_date')->nullable();
            $table->string('pi_identification_code')->nullable();
            $table->string('branch_identification_code')->nullable();
            $table->string('borrowers_client_number')->nullable();
            $table->string('borrower_classification')->nullable();
            $table->string('credit_account_reference')->nullable();
            $table->string('credit_account_date')->nullable();
            $table->string('credit_amount')->nullable();
            $table->string('credit_amount_ugx_equivalent')->nullable();
            $table->string('facility_amount_granted')->nullable();
            $table->string('credit_amount_drawdown')->nullable();
            $table->string('credit_amount_drawdown_ugx_equivalent')->nullable();
            $table->string('credit_account_type')->nullable();
            $table->string('group_identification_joint_account_number')->nullable();
            $table->string('transaction_date')->nullable();
            $table->string('currency')->nullable();
            $table->string('opening_balance_indicator')->nullable();
            $table->string('maturity_date')->nullable();
            $table->string('type_of_interest')->nullable();
            $table->string('interest_calculation_method')->nullable();
            $table->string('annual_interest_rate_at_disbursement')->nullable();
            $table->string('annual_interest_rate_at_reporting')->nullable();
            $table->string('date_of_first_payment')->nullable();
            $table->string('credit_amortization_type')->nullable();
            $table->string('credit_payment_frequency')->nullable();
            $table->string('number_of_payments')->nullable();
            $table->string('monthly_instalment_amount')->nullable();
            $table->string('current_balance_amount')->nullable();
            $table->string('current_balance_amount_ugx_equivalent')->nullable();
            $table->string('current_balance_indicator')->nullable();
            $table->string('last_payment_date')->nullable();
            $table->string('last_payment_amount')->nullable();
            $table->string('credit_account_status')->nullable();
            $table->string('last_status_change_date')->nullable();
            $table->string('credit_account_risk_classification')->nullable();
            $table->string('credit_account_arrears_date')->nullable();
            $table->string('number_of_days_in_arrears')->nullable();
            $table->string('balance_overdue')->nullable();
            $table->string('flag_for_restructured_credit')->nullable();
            $table->string('old_branch_code')->nullable();
            $table->string('old_account_number')->nullable();
            $table->string('old_client_number')->nullable();
            $table->string('balance_overdue_indicator')->nullable();
            $table->string('credit_account_closure_date')->nullable();
            $table->string('credit_account_closure_reason')->nullable();
            $table->string('specific_provision_amount')->nullable();
            $table->string('client_consent_flag')->nullable();
            $table->string('client_advice_notice_flag')->nullable();
            $table->string('term')->nullable();
            $table->string('loan_purpose')->nullable();
            $table->string('group_joint_account_exposure_amount')->nullable();
            $table->string('flag_for_group')->nullable();
            $table->string('flag_for_joint_account')->nullable();
            $table->string('ii_registration_certificate_number')->nullable();
            $table->string('ii_tax_identification_number')->nullable();
            $table->string('ii_value_added_tax_number')->nullable();
            $table->string('ii_fcs_number')->nullable();
            $table->string('ii_passport_number')->nullable();
            $table->string('ii_drivers_licence_id_number')->nullable();
            $table->string('ii_voters_perno')->nullable();
            $table->string('ii_drivers_license_permit_number')->nullable();
            $table->string('ii_nssf_number')->nullable();
            $table->string('ii_country_id')->nullable();
            $table->string('ii_country_issuing_authority')->nullable();
            $table->string('ii_nationality')->nullable();
            $table->string('ii_police_id_number')->nullable();
            $table->string('ii_updf_number')->nullable();
            $table->string('ii_kacita_license_number')->nullable();
            $table->string('ii_public_service_pension_number')->nullable();
            $table->string('ii_teacher_registration_number')->nullable();
            $table->string('ii_country_of_issue')->nullable();
            $table->string('gscafb_business_name')->nullable();
            $table->string('gscafb_trading_name')->nullable();
            $table->string('gscafb_activity_description')->nullable();
            $table->string('gscafb_industry_sector_code')->nullable();
            $table->string('gscafb_date_registered')->nullable();
            $table->string('gscafb_business_type_code')->nullable();
            $table->string('gscafb_surname')->nullable();
            $table->string('gscafb_forename1')->nullable();
            $table->string('gscafb_forename2')->nullable();
            $table->string('gscafb_forename3')->nullable();
            $table->string('gscafb_gender')->nullable();
            $table->string('gscafb_marital_status')->nullable();
            $table->string('gscafb_date_of_birth')->nullable();
            $table->string('ei_employment_type')->nullable();
            $table->string('ei_primary_occupation')->nullable();
            $table->string('ei_employer_name')->nullable();
            $table->string('ei_employee_number')->nullable();
            $table->string('ei_employment_date')->nullable();
            $table->string('ei_income_band')->nullable();
            $table->string('ei_salary_frequency')->nullable();
            $table->string('pci_unit_number')->nullable();
            $table->string('pci_building_name')->nullable();
            $table->string('pci_floor_number')->nullable();
            $table->string('pci_plot_or_street_number')->nullable();
            $table->string('pci_lc_or_street_name')->nullable();
            $table->string('pci_parish')->nullable();
            $table->string('pci_suburb')->nullable();
            $table->string('pci_village')->nullable();
            $table->string('pci_county_or_town')->nullable();
            $table->string('pci_district')->nullable();
            $table->string('pci_region')->nullable();
            $table->string('pci_po_box_number')->nullable();
            $table->string('pci_post_office_town')->nullable();
            $table->string('pci_country_code')->nullable();
            $table->string('pci_period_at_address')->nullable();
            $table->string('pci_flag_of_ownership')->nullable();
            $table->string('pci_primary_number_country_dialling_code')->nullable();
            $table->string('pci_primary_number_telephone_number')->nullable();
            $table->string('pci_other_number_country_dialling_code')->nullable();
            $table->string('pci_other_number_telephone_number')->nullable();
            $table->string('pci_mobile_number_country_dialling_code')->nullable();
            $table->string('pci_mobile_number_telephone_number')->nullable();
            $table->string('pci_facsimile_country_dialling_code')->nullable();
            $table->string('pci_facsimile_number')->nullable();
            $table->string('pci_email_address')->nullable();
            $table->string('pci_web_site')->nullable();
            $table->string('pci_latitude')->nullable();
            $table->string('pci_longitude')->nullable();
            $table->string('sci_unit_number')->nullable();
            $table->string('sci_unit_name')->nullable();
            $table->string('sci_floor_number')->nullable();
            $table->string('sci_plot_or_street_number')->nullable();
            $table->string('sci_lc_or_street_name')->nullable();
            $table->string('sci_parish')->nullable();
            $table->string('sci_suburb')->nullable();
            $table->string('sci_village')->nullable();
            $table->string('sci_county_or_town')->nullable();
            $table->string('sci_district')->nullable();
            $table->string('sci_region')->nullable();
            $table->string('sci_po_box_number')->nullable();
            $table->string('sci_post_office_town')->nullable();
            $table->string('sci_country_code')->nullable();
            $table->string('sci_period_at_address')->nullable();
            $table->string('sci_flag_for_ownership')->nullable();
            $table->string('sci_primary_number_country_dialling_code')->nullable();
            $table->string('sci_primary_number_telephone_number')->nullable();
            $table->string('sci_other_number_country_dialling_code')->nullable();
            $table->string('sci_other_number_telephone_number')->nullable();
            $table->string('sci_mobile_number_country_dialling_code')->nullable();
            $table->string('sci_mobile_number_telephone_number')->nullable();
            $table->string('sci_facsimile_country_dialling_code')->nullable();
            $table->string('sci_facsimile_number')->nullable();
            $table->string('sci_email_address')->nullable();
            $table->string('sci_web_site')->nullable();
            $table->string('sci_latitude')->nullable();
            $table->string('sci_longitude')->nullable();
            $table->integer('validationstatus')->nullable()->default(0);
            $table->integer('uploadstatus')->nullable()->default(0);
            $table->integer('is_moved')->nullable()->default(0);
            $table->string('createdby')->nullable();
            $table->timestamp('datecreated')->nullable()->default(Carbon::now());
            $table->string('modifiedby')->nullable();
            $table->timestamp('datemodified')->nullable()->default(Carbon::now());
            $table->string('old_pi_identification_code')->after('')->nullable();
            $table->string('mode_of_restructure')->after('')->nullable();
            $table->string('risk_classification_criteria')->after('')->nullable();
            $table->string('ii_refugee_number')->after('')->nullable();
            $table->string('ii_work_permit_number')->after('')->nullable();
        });

        Schema::table($table_name, function (Blueprint $table) {
            $table->index('credit_account_reference');
        });
    }
}

if (! function_exists('create_cap')) {
    function create_cap(string $table_name): void
    {
        Schema::create($table_name, function (Blueprint $table) {
            $table->increments('id');
            $table->string('submission_date')->nullable();
            $table->string('pi_identification_code')->nullable();
            $table->string('branch_identification_code')->nullable();
            $table->string('client_number')->nullable();
            $table->string('credit_application_reference')->nullable();
            $table->string('applicant_classification')->nullable();
            $table->string('credit_application_date')->nullable();
            $table->string('amount')->nullable();
            $table->string('currency')->nullable();
            $table->string('credit_account_or_loan_product_type')->nullable();
            $table->string('credit_application_status')->nullable();
            $table->string('last_status_change_date')->nullable();
            $table->string('credit_application_duration')->nullable();
            $table->string('rejection_reason')->nullable();
            $table->string('client_consent_flag')->nullable();
            $table->string('group_identification_joint_account_number')->nullable();
            $table->string('ii_registration_certificate_number')->nullable();
            $table->string('ii_tax_identification_number')->nullable();
            $table->string('ii_value_added_tax_number')->nullable();
            $table->string('ii_fcs_number')->nullable();
            $table->string('ii_passport_number')->nullable();
            $table->string('ii_drivers_licence_id_number')->nullable();
            $table->string('ii_voters_perno')->nullable();
            $table->string('ii_drivers_license_permit_number')->nullable();
            $table->string('ii_nssf_number')->nullable();
            $table->string('ii_country_id')->nullable();
            $table->string('ii_country_issuing_authority')->nullable();
            $table->string('ii_nationality')->nullable();
            $table->string('ii_police_id_number')->nullable();
            $table->string('ii_updf_number')->nullable();
            $table->string('ii_kacita_license_number')->nullable();
            $table->string('ii_public_service_pension_number')->nullable();
            $table->string('ii_teacher_registration_number')->nullable();
            $table->string('ii_country_of_issue')->nullable();
            $table->string('gscafb_business_name')->nullable();
            $table->string('gscafb_trading_name')->nullable();
            $table->string('gscafb_activity_description')->nullable();
            $table->string('gscafb_industry_sector_code')->nullable();
            $table->string('gscafb_date_registered')->nullable();
            $table->string('gscafb_business_type_code')->nullable();
            $table->string('gscafb_surname')->nullable();
            $table->string('gscafb_forename1')->nullable();
            $table->string('gscafb_forename2')->nullable();
            $table->string('gscafb_forename3')->nullable();
            $table->string('gscafb_gender')->nullable();
            $table->string('gscafb_marital_status')->nullable();
            $table->string('gscafb_date_of_birth')->nullable();
            $table->string('ei_employment_type')->nullable();
            $table->string('ei_primary_occupation')->nullable();
            $table->string('ei_employer_name')->nullable();
            $table->string('ei_employee_number')->nullable();
            $table->string('ei_employment_date')->nullable();
            $table->string('ei_income_band')->nullable();
            $table->string('ei_salary_frequency')->nullable();
            $table->string('pci_unit_number')->nullable();
            $table->string('pci_building_name')->nullable();
            $table->string('pci_floor_number')->nullable();
            $table->string('pci_plot_or_street_number')->nullable();
            $table->string('pci_lc_or_street_name')->nullable();
            $table->string('pci_parish')->nullable();
            $table->string('pci_suburb')->nullable();
            $table->string('pci_village')->nullable();
            $table->string('pci_county_or_town')->nullable();
            $table->string('pci_district')->nullable();
            $table->string('pci_region')->nullable();
            $table->string('pci_po_box_number')->nullable();
            $table->string('pci_post_office_town')->nullable();
            $table->string('pci_country_code')->nullable();
            $table->string('pci_period_at_address')->nullable();
            $table->string('pci_flag_of_ownership')->nullable();
            $table->string('pci_primary_number_country_dialling_code')->nullable();
            $table->string('pci_primary_number_telephone_number')->nullable();
            $table->string('pci_other_number_country_dialling_code')->nullable();
            $table->string('pci_other_number_telephone_number')->nullable();
            $table->string('pci_mobile_number_country_dialling_code')->nullable();
            $table->string('pci_mobile_number_telephone_number')->nullable();
            $table->string('pci_facsimile_country_dialling_code')->nullable();
            $table->string('pci_facsimile_number')->nullable();
            $table->string('pci_email_address')->nullable();
            $table->string('pci_web_site')->nullable();
            $table->string('pci_latitude')->nullable();
            $table->string('pci_longitude')->nullable();
            $table->string('sci_unit_number')->nullable();
            $table->string('sci_unit_name')->nullable();
            $table->string('sci_floor_number')->nullable();
            $table->string('sci_plot_or_street_number')->nullable();
            $table->string('sci_lc_or_street_name')->nullable();
            $table->string('sci_parish')->nullable();
            $table->string('sci_suburb')->nullable();
            $table->string('sci_village')->nullable();
            $table->string('sci_county_or_town')->nullable();
            $table->string('sci_district')->nullable();
            $table->string('sci_region')->nullable();
            $table->string('sci_po_box_number')->nullable();
            $table->string('sci_post_office_town')->nullable();
            $table->string('sci_country_code')->nullable();
            $table->string('sci_period_at_address')->nullable();
            $table->string('sci_flag_for_ownership')->nullable();
            $table->string('sci_primary_number_country_dialling_code')->nullable();
            $table->string('sci_primary_number_telephone_number')->nullable();
            $table->string('sci_other_number_country_dialling_code')->nullable();
            $table->string('sci_other_number_telephone_number')->nullable();
            $table->string('sci_mobile_number_country_dialling_code')->nullable();
            $table->string('sci_mobile_number_telephone_number')->nullable();
            $table->string('sci_facsimile_country_dialling_code')->nullable();
            $table->string('sci_facsimile_number')->nullable();
            $table->string('sci_email_address')->nullable();
            $table->string('sci_web_site')->nullable();
            $table->string('sci_latitude')->nullable();
            $table->string('sci_longitude')->nullable();
            $table->integer('validationstatus')->nullable()->default(0);
            $table->integer('uploadstatus')->nullable()->default(0);
            $table->integer('is_moved')->nullable()->default(0);
            $table->string('createdby')->nullable();
            $table->timestamp('datecreated')->nullable()->default(Carbon::now());
            $table->string('modifiedby')->nullable();
            $table->timestamp('datemodified')->nullable()->default(Carbon::now());
            $table->string('amount_approved')->after('group_identification_joint_account_number')->nullable();
            $table->string('approved_currency')->after('amount_approved')->nullable();
            $table->string('ii_refugee_number')->after('ii_country_of_issue')->nullable();
            $table->string('ii_work_permit_number')->after('ii_refugee_number')->nullable();
        });

        Schema::table($table_name, function (Blueprint $table) {
            $table->index('credit_application_reference');
        });
    }
}

if (! function_exists('create_cmc')) {
    function create_cmc(string $table_name): void
    {
        Schema::create($table_name, function (Blueprint $table) {
            $table->increments('id');
            $table->string('submission_date')->nullable();
            $table->string('pi_identification_code')->nullable();
            $table->string('branch_identification_code')->nullable();
            $table->string('borrowers_client_number')->nullable();
            $table->string('borrower_account_reference')->nullable();
            $table->string('borrower_classification')->nullable();
            $table->string('collateral_type_identification')->nullable();
            $table->string('collateral_reference_number')->nullable();
            $table->text('collateral_description')->nullable();
            $table->string('collateral_currency')->nullable();
            $table->string('collateral_open_market_value')->nullable();
            $table->string('collateral_forced_sale_value')->nullable();
            $table->string('collateral_valuation_expiry_date')->nullable();
            $table->string('instrument_of_claim')->nullable();
            $table->string('valuation_date')->nullable();
            $table->integer('validationstatus')->nullable()->default(0);
            $table->integer('uploadstatus')->nullable()->default(0);
            $table->integer('is_moved')->nullable()->default(0);
            $table->string('createdby')->nullable();
            $table->timestamp('datecreated')->nullable()->default(Carbon::now());
            $table->string('modifiedby')->nullable();
            $table->timestamp('datemodified')->nullable()->default(Carbon::now());
        });

        Schema::table($table_name, function (Blueprint $table) {
            $table->index('borrowers_client_number');
        });
    }
}

if (! function_exists('create_ccg')) {
    function create_ccg(string $table_name): void
    {
        Schema::create($table_name, function (Blueprint $table) {
            $table->increments('id');
            $table->string('submission_date')->nullable();
            $table->string('pi_identification_code')->nullable();
            $table->string('branch_identification_code')->nullable();
            $table->string('borrowers_client_number')->nullable();
            $table->string('borrower_account_reference')->nullable();
            $table->string('guarantor_classification')->nullable();
            $table->string('guarantee_type')->nullable();
            $table->string('guarantor_type')->nullable();
            $table->string('group_identification_joint_account_number')->nullable();
            $table->string('ii_registration_certificate_number')->nullable();
            $table->string('ii_tax_identification_number')->nullable();
            $table->string('ii_value_added_tax_number')->nullable();
            $table->string('ii_fcs_number')->nullable();
            $table->string('ii_passport_number')->nullable();
            $table->string('ii_drivers_licence_id_number')->nullable();
            $table->string('ii_voters_perno')->nullable();
            $table->string('ii_drivers_license_permit_number')->nullable();
            $table->string('ii_nssf_number')->nullable();
            $table->string('ii_country_id')->nullable();
            $table->string('ii_country_issuing_authority')->nullable();
            $table->string('ii_nationality')->nullable();
            $table->string('ii_police_id_number')->nullable();
            $table->string('ii_updf_number')->nullable();
            $table->string('ii_kacita_license_number')->nullable();
            $table->string('ii_public_service_pension_number')->nullable();
            $table->string('ii_teacher_registration_number')->nullable();
            $table->string('ii_country_of_issue')->nullable();
            $table->string('gscafb_business_name')->nullable();
            $table->string('gscafb_trading_name')->nullable();
            $table->string('gscafb_activity_description')->nullable();
            $table->string('gscafb_industry_sector_code')->nullable();
            $table->string('gscafb_date_registered')->nullable();
            $table->string('gscafb_business_type_code')->nullable();
            $table->string('gscafb_surname')->nullable();
            $table->string('gscafb_forename1')->nullable();
            $table->string('gscafb_forename2')->nullable();
            $table->string('gscafb_forename3')->nullable();
            $table->string('gscafb_gender')->nullable();
            $table->string('gscafb_marital_status')->nullable();
            $table->string('gscafb_date_of_birth')->nullable();
            $table->string('ei_employment_type')->nullable();
            $table->string('ei_primary_occupation')->nullable();
            $table->string('ei_employer_name')->nullable();
            $table->string('ei_employee_number')->nullable();
            $table->string('ei_employment_date')->nullable();
            $table->string('ei_income_band')->nullable();
            $table->string('ei_salary_frequency')->nullable();
            $table->string('pci_unit_number')->nullable();
            $table->string('pci_building_name')->nullable();
            $table->string('pci_floor_number')->nullable();
            $table->string('pci_plot_or_street_number')->nullable();
            $table->string('pci_lc_or_street_name')->nullable();
            $table->string('pci_parish')->nullable();
            $table->string('pci_suburb')->nullable();
            $table->string('pci_village')->nullable();
            $table->string('pci_county_or_town')->nullable();
            $table->string('pci_district')->nullable();
            $table->string('pci_region')->nullable();
            $table->string('pci_po_box_number')->nullable();
            $table->string('pci_post_office_town')->nullable();
            $table->string('pci_country_code')->nullable();
            $table->string('pci_period_at_address')->nullable();
            $table->string('pci_flag_of_ownership')->nullable();
            $table->string('pci_primary_number_country_dialling_code')->nullable();
            $table->string('pci_primary_number_telephone_number')->nullable();
            $table->string('pci_other_number_country_dialling_code')->nullable();
            $table->string('pci_other_number_telephone_number')->nullable();
            $table->string('pci_mobile_number_country_dialling_code')->nullable();
            $table->string('pci_mobile_number_telephone_number')->nullable();
            $table->string('pci_facsimile_country_dialling_code')->nullable();
            $table->string('pci_facsimile_number')->nullable();
            $table->string('pci_email_address')->nullable();
            $table->string('pci_web_site')->nullable();
            $table->string('pci_latitude')->nullable();
            $table->string('pci_longitude')->nullable();
            $table->string('sci_unit_number')->nullable();
            $table->string('sci_unit_name')->nullable();
            $table->string('sci_floor_number')->nullable();
            $table->string('sci_plot_or_street_number')->nullable();
            $table->string('sci_lc_or_street_name')->nullable();
            $table->string('sci_parish')->nullable();
            $table->string('sci_suburb')->nullable();
            $table->string('sci_village')->nullable();
            $table->string('sci_county_or_town')->nullable();
            $table->string('sci_district')->nullable();
            $table->string('sci_region')->nullable();
            $table->string('sci_po_box_number')->nullable();
            $table->string('sci_post_office_town')->nullable();
            $table->string('sci_country_code')->nullable();
            $table->string('sci_period_at_address')->nullable();
            $table->string('sci_flag_for_ownership')->nullable();
            $table->string('sci_primary_number_country_dialling_code')->nullable();
            $table->string('sci_primary_number_telephone_number')->nullable();
            $table->string('sci_other_number_country_dialling_code')->nullable();
            $table->string('sci_other_number_telephone_number')->nullable();
            $table->string('sci_mobile_number_country_dialling_code')->nullable();
            $table->string('sci_mobile_number_telephone_number')->nullable();
            $table->string('sci_facsimile_country_dialling_code')->nullable();
            $table->string('sci_facsimile_number')->nullable();
            $table->string('sci_email_address')->nullable();
            $table->string('sci_web_site')->nullable();
            $table->string('sci_latitude')->nullable();
            $table->string('sci_longitude')->nullable();
            $table->integer('validationstatus')->nullable()->default(0);
            $table->integer('uploadstatus')->nullable()->default(0);
            $table->integer('is_moved')->nullable()->default(0);
            $table->string('createdby')->nullable();
            $table->timestamp('datecreated')->nullable()->default(Carbon::now());
            $table->string('modifiedby')->nullable();
            $table->timestamp('datemodified')->nullable()->default(Carbon::now());
            $table->string('ii_refugee_number')->after('ii_country_of_issue')->nullable();
            $table->string('ii_work_permit_number')->after('ii_refugee_number')->nullable();
        });

        Schema::table($table_name, function (Blueprint $table) {
            $table->index('borrower_account_reference');
        });
    }
}

if (! function_exists('mapFileNameToModel')) {
    function mapFileNameToModel(string $filename): ?string
    {
        $classes = [
            'BC' => BouncedCheque::class,
            'BS' => BorrowerStakeholder::class,
            'CAP' => CreditApplication::class,
            'CBA' => CreditBorrowerAccount::class,
            'CCG' => CollateralCreditGuarantor::class,
            'CMC' => CollateralMaterialCollateral::class,
            'FRA' => FinancialMalpracticeData::class,
            'IB' => InstitutionBranch::class,
            'PI' => ParticipatingInstitution::class,
            'PIS' => ParticipatingInstitutionStakeholder::class,
        ];

        return Arr::get($classes, $filename);
    }
}

if (! function_exists('format_date')) {
    function format_date(string $date): string
    {
        return Carbon::createFromFormat('Ymd', $date)->toDayDateTimeString();
    }
}

if (! function_exists('mapFileNameToItsFullname')) {
    function mapFileNameToItsFullname(string $filename): ?string
    {
        $fullNames = [
            'BC' => 'Bounced Cheques',
            'BS' => 'Borrower Stakeholders',
            'CAP' => 'Credit Applications',
            'CBA' => 'Credit Borrower Accounts',
            'CCG' => 'Collateral Credit Guarantors',
            'CMC' => 'Collateral Material Collateral',
            'FRA' => 'Financial Malpractice',
            'IB' => 'Institution Branches',
            'PI' => 'Participating Institutions',
            'PIS' => 'Participating Institution Stakeholders',
        ];

        return Arr::get($fullNames, $filename);
    }
}

if (! function_exists('generate_scores')) {

    /**
     * @param  Collection<int, object>  $caps
     * @param  Collection<int, object>  $cbas
     * @return array<string, int|string>
     *
     * @codeCoverageIgnoreStart
     */
    function generate_scores(Collection $caps, Collection $cbas, Carbon $submission_date, int|string $individual_id): array
    {
        return [
            'closed_account_12m' => CreditScore::getClosedAccounts12Months($cbas, $submission_date),
            'accounts_ever_30days_in_arrears_2yrs' => CreditScore::getAccountsOver30daysInArrears2years($cbas, $submission_date),
            'accounts_ever_greater_than_90_days_in_arrears_6months' => CreditScore::getAccountsOver90daysInArrears6Months($cbas, $submission_date),
            'total_current_balance_amount_in_the_last_24month' => CreditScore::getCurrentBalanceAmount24Months($cbas, $submission_date),
            'closed_unsecured_loans' => CreditScore::getClosedUnsecuredLoans($cbas, $submission_date),
            'credit_applications_12_months' => CreditScore::getCreditApplications12Months($caps, $submission_date),
            'value_of_credit_applications_12months' => CreditScore::valueOfCreditApplications12Months($caps, $submission_date),
            'banks_borrowed_from' => CreditScore::getBanksBorrowedFrom($cbas, $submission_date),
            'ratio_of_12month_to_24month_balance' => CreditScore::getRatioOf12monthTo24monthBalance($cbas, $submission_date),
            'submission_date' => $submission_date->toDateString(),
            'individual_id' => $individual_id,
        ];
    }
    /** @codeCoverageIgnoreEnd */
}

if (! function_exists('is_alpha_numeric')) {
    function is_alpha_numeric(string $str): bool
    {
        return (bool) preg_match('/^[a-zA-Z0-9\_\-\;\:\/\\\?\*\!\+\@\&\£\$\€\<\>\#\%\s\[\]\"\=\.\'\(\)\,]+$/i', $str);
    }
}

if (! function_exists('not_alpha_numeric') && function_exists('is_alpha_numeric')) {
    function not_alpha_numeric(string $str): bool
    {
        return is_alpha_numeric($str) === false;
    }
}

if (! function_exists('pi_file_type')) {
    /**
     * Get the file type from the filename uploaded for validation
     * E.g. for a filename such as 'CB00520231031CBA.CSV' this function will return 'CBA'.
     */
    function pi_file_type(string $piCode, string $file): string
    {
        /**
         * Just in case will receive a file with path details,
         * We will take out the filename only.
         */
        $filename = basename($file);

        return str($filename)
            ->after(str($filename)->after($piCode)->take(8)->toString())
            ->before('.')
            ->toString();
    }
}

if (! function_exists('pi_submission_date')) {
    function pi_submission_date(string $piCode, string $file): string
    {
        return str(basename($file))
            ->after($piCode)
            ->take(8)
            ->toString();
    }
}

if (! function_exists('validate_date')) {
    function validate_date(string $date, string $format = 'Ymd'): bool
    {
        /**
         * Ensure that the string passed is made of integers
         * and that string is always 8 characters long.
         * Here we avoid most of the possible errors on dates.
         */
        if (strlen((string) ((int) $date)) !== 8) {
            return false;
        }

        try {
            $d = DateTime::createFromFormat($format, $date);

            return $d && $d->format($format) === $date;
        } catch (\Exception $e) {
            return false;
        }
    }
}

if (! function_exists('save_progress')) {
    function save_progress(string $piCode, int $progress, string $message, ?string $batchId = null): void
    {
        $uiProgress = (array) Cache::get($piCode.'_validation_progress', []);
        Arr::set(
            $uiProgress,
            'general_progress',
            [
                'progress' => $progress,
                'message' => $message,
                'batch_id' => $batchId,
            ]);

        Cache::put($piCode.'_validation_progress', $uiProgress);
    }
}

if (! function_exists('save_upload_progress')) {
    function save_upload_progress(int $progress, string $message, ?string $batchId = ''): void
    {
        $uiProgress = (array) Cache::get('upload_progress', [
            'general_progress' => [
                'progress' => 0,
                'message' => '',
                'batch_id' => '',
            ],
        ]);

        Arr::set(
            $uiProgress,
            'general_progress',
            [
                'progress' => $progress,
                'message' => $message,
                'batch_id' => $batchId,
            ]);

        Cache::put('upload_progress', $uiProgress);
    }
}

if (! function_exists('get_record_classification')) {
    function get_record_classification($recordType): string
    {
        return [
            'BS' => 'stakeholder_type',
            'CAP' => 'applicant_classification',
            'PIS' => 'stakeholder_type',
            'CCG' => 'guarantor_classification',
            'BC' => 'pi_client_classification',
            'CBA' => 'borrower_classification',
            'FRA' => 'consumer_classification',
            'CMC' => 'borrower_classification',
        ][$recordType] ?? '';
    }
}
