<?php

namespace App\Listeners;

use App\Events\FileValidationCompleted;
use App\Models\User;
use App\Models\Validator\DataSubmission;
use App\Notifications\ValidationCompleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Notification;

class NotifyPIUsers implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FileValidationCompleted $event): void
    {
        $users = User::query()->where('pi_code', $event->piCode)->get();

        if ($users->isEmpty()) {
            return;
        }

        Notification::send($users, new ValidationCompleted($event->piCode, $event->submissionDate));
    }

    /**
     * Only notify users when all files have been validated.
     */
    public function shouldQueue(FileValidationCompleted $event): bool
    {
        $shouldNotifyPI = cache()->get('notify_pi', true);

        if ($shouldNotifyPI === false) {
            return false;
        }

        return DataSubmission::query()
            ->where([
                'validationstatus' => 0,
                'pi_identification_code' => $event->piCode,
                'submission_date' => $event->submissionDate,
                'submission_type' => $event->submissionType,
                'processing' => 1,
            ])->doesntExist();
    }
}
