<?php

namespace App\Listeners;

use App\Events\FileValidationCompleted;
use App\Jobs\UI\Reports\ZipValidationReportsJob;
use App\Models\Validator\DataSubmission;
use Illuminate\Contracts\Queue\ShouldQueue;

class ZipValidationReports implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FileValidationCompleted $event): void
    {
        $submission = DataSubmission::query()
            ->where([
                'pi_identification_code' => $event->piCode,
                'validationstatus' => 1,
                'submission_date' => $event->submissionDate,
                'submission_type' => $event->submissionType,
            ])
            ->first();

        if (empty($submission)) {
            return;
        }

        dispatch(new ZipValidationReportsJob($submission));
    }

    /**
     * Only zip files when all files have been validated.
     */
    public function shouldQueue(FileValidationCompleted $event): bool
    {
        return DataSubmission::query()
            ->where([
                'validationstatus' => 0,
                'pi_identification_code' => $event->piCode,
                'submission_date' => $event->submissionDate,
                'submission_type' => $event->submissionType,
                'processing' => 1,
            ])->doesntExist();
    }
}
