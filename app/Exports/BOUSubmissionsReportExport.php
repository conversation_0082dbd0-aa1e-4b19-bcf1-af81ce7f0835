<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class BOUSubmissionsReportExport implements WithMultipleSheets
{
    use Exportable;

    public function __construct(protected string $submissionDate, protected string $submissionType) {}

    /**
     * @return array<string>
     */
    public function sheets(): array
    {
        return [
            new BOUSubmissionsSuccessRecordsExport($this->submissionDate, $this->submissionType),
            new BOUSubmissionsSuccessRateExport($this->submissionDate, $this->submissionType),
        ];
    }
}
