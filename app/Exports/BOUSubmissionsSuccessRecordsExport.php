<?php

namespace App\Exports;

use App\Models\Validator\ValidationSummary;
use App\Utilities\ParticipatingInstitutionsUtility;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BOUSubmissionsSuccessRecordsExport implements FromView, WithDrawings, WithStyles, WithTitle
{
    public function __construct(protected string $submissionDate, protected string $submissionType) {}

    public function view(): View
    {
        $institutions = (new ParticipatingInstitutionsUtility)->all();
        $bouOrder = ['CBA', 'CAP', 'BC', 'CCG', 'CMC', 'BS', 'PIS', 'PI', 'IB', 'FRA'];
        $summaries = ValidationSummary::query()
            ->select(['pi_identification_code', 'submission_date', 'creation_date', 'file', 'submitted', 'passed', 'failed'])
            ->where('submission_date', $this->submissionDate)
            ->where('submission_type', $this->submissionType)
            ->get()
            // Append the PI name onto each record since we are not accessing the pi names from the dictionary
            ->map(function (ValidationSummary $summary) use ($institutions) {
                if ($summary->submitted === 0) {
                    $summary->pass_percentage = 0;
                }

                $summary->pi_name = Arr::get($institutions, $summary->pi_identification_code);

                return $summary;
            })
            ->groupBy('pi_name')
            ->mapWithKeys(function (Collection $piSummaries, $piName) use ($bouOrder) {
                return [
                    $piName => collect($bouOrder)
                        ->mapWithKeys(function ($file) use ($piSummaries) {
                            $summary = $piSummaries->firstWhere('file', $file);

                            if ($summary) {
                                return [$file => $summary->only(['submitted', 'passed', 'file'])];
                            }

                            return [$file => ['submitted' => 0, 'passed' => 0, 'file' => $file]];
                        })->toArray(),
                ];
            })
            // Sort Alphabetically on PI Names
            ->sortBy(function ($group, $key) {
                return $key;
            });

        return view('admin.exports.bou.submissions-success-records', [
            'summaries' => $summaries,
            'fileOrder' => $bouOrder,
            'submissionDate' => Carbon::createFromFormat('Ymd', $this->submissionDate)->format('m/Y'),
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        return [
            'A6' => ['alignment' => ['wrapText' => true]],
            'A8' => ['alignment' => ['wrapText' => true]],
        ];
    }

    public function drawings()
    {
        $drawing = new Drawing;
        $drawing->setPath(public_path('images/bou_logo.png'));
        $drawing->setHeight(85);
        $drawing->setCoordinates('D1');

        return $drawing;
    }

    public function title(): string
    {
        return 'SFIs';
    }
}
