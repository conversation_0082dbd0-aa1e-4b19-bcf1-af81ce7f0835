<?php

namespace App\Filament\Resources\ParticipatingInstitutionResource\Pages;

use App\Filament\Resources\ParticipatingInstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListParticipatingInstitutions extends ListRecords
{
    protected static string $resource = ParticipatingInstitutionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
