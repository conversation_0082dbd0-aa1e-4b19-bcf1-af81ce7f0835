<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql')->create('borrower_stakeholders', function (Blueprint $table) {
            $table->string('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->string('borrowers_client_number', 30);
            $table->integer('stakeholder_type')->nullable();
            $table->integer('stakeholder_category')->nullable();
            $table->decimal('shareholder_percentage', 5)->nullable();
            $table->integer('individual_id')->nullable()->index('borrower_stakeholders_ind_inx');
            $table->integer('non_individual_id')->nullable()->index('borrower_stakeholders_nonind_inx');
            $table->date('submission_date')->nullable();

            $table->primary(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number']);
        });

        Schema::connection('mysql')->create('bounced_cheques', function (Blueprint $table) {
            $table->char('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->string('client_number', 30);
            $table->integer('pi_client_classification');
            $table->string('cheque_account_reference_number', 30);
            $table->date('cheque_account_opened_date');
            $table->integer('cheque_account_classification');
            $table->integer('cheque_account_type');
            $table->integer('cheque_number');
            $table->decimal('cheque_amount', 19)->nullable();
            $table->char('cheque_currency', 3)->nullable();
            $table->string('beneficiary_name_or_payee', 50)->nullable();
            $table->date('cheque_bounce_date');
            $table->integer('cheque_account_bounce_reason');
            $table->integer('individual_id')->nullable()->index('bounced_cheques_indx');
            $table->integer('nonindividual_id')->nullable()->index('bounced_cheques_nonin_indx');
            $table->integer('submission_date')->nullable();

            $table->primary(['pi_identification_code', 'branch_identification_code', 'client_number', 'cheque_account_reference_number', 'cheque_number']);
        });

        Schema::connection('mysql')->create('branch_addresses', function (Blueprint $table) {
            $table->integer('addresses_id', true);
            $table->char('pi_identification_code', 6)->nullable();
            $table->char('branch_identification_code', 3)->nullable();
            $table->string('pci_unit_number', 10)->nullable();
            $table->string('pci_building_name', 50)->nullable();
            $table->string('pci_floor_number', 30)->nullable();
            $table->string('pci_plot_or_street_number', 10)->nullable();
            $table->string('pci_lc_or_street_name', 50)->nullable();
            $table->string('pci_parish', 50)->nullable();
            $table->string('pci_suburb', 50)->nullable();
            $table->string('pci_village', 50)->nullable();
            $table->string('pci_county_or_town', 50)->nullable();
            $table->string('pci_district', 50)->nullable();
            $table->integer('pci_region')->nullable();
            $table->string('pci_po_box_number', 10)->nullable();
            $table->string('pci_post_office_town', 20)->nullable();
            $table->char('pci_country_code', 2)->nullable();
            $table->integer('pci_period_at_address')->nullable();
            $table->char('pci_flag_of_ownership', 1)->nullable();
            $table->string('pci_email_address', 150)->nullable();
            $table->string('pci_web_site', 50)->nullable();
            $table->date('submission_date')->nullable();

            $table->unique(['pi_identification_code', 'branch_identification_code'], 'branch_branch_addresses_fk');
        });

        Schema::connection('mysql')->create('branch_information', function (Blueprint $table) {
            $table->char('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->string('branch_name', 100);
            $table->char('branch_type', 1);
            $table->date('date_opened');
            $table->date('submission_date')->nullable();

            $table->index(['pi_identification_code', 'branch_identification_code'], 'branch_information_indx');
            $table->primary(['pi_identification_code', 'branch_identification_code']);
        });

        Schema::connection('mysql')->create('branch_telephone_numbers', function (Blueprint $table) {
            $table->integer('telephone_number_id', true);
            $table->char('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->integer('pci_primary_number_country_dialling_code')->nullable();
            $table->bigInteger('pci_primary_number_telephone_number')->nullable();
            $table->integer('pci_other_number_country_dialling_code')->nullable();
            $table->bigInteger('pci_other_number_telephone_number')->nullable();
            $table->integer('pci_mobile_number_country_dialling_code')->nullable();
            $table->bigInteger('pci_mobile_number_telephone_number')->nullable();
            $table->integer('pci_facsimile_country_dialling_code')->nullable();
            $table->bigInteger('pci_facsimile_number')->nullable();
            $table->date('submission_date')->nullable();

            $table->unique(['pi_identification_code', 'branch_identification_code'], 'branch_branch_telephonenumbers_fk');
        });

        Schema::connection('mysql')->create('collateral_credit_guarantor', function (Blueprint $table) {
            $table->char('pi_identification_code', 6);
            $table->char('branch_identification_code', 3)->index('ccg_brcodeq_indx');
            $table->string('borrowers_client_number', 30)->index('ccg_clientq_indx');
            $table->string('borrower_account_reference', 30)->index('ccg_borrow_accounnt_indx');
            $table->integer('guarantor_classification');
            $table->integer('guarantee_type');
            $table->integer('guarantor_type');
            $table->bigInteger('group_identification_joint_account_number')->nullable();
            $table->integer('individual_id')->nullable()->index('collateral_credit_guator_ind_indx');
            $table->integer('non_individual_id')->nullable()->index('ccg_nonindiviidual_id_fk');
            $table->date('submission_Date')->nullable();

            $table->index(['pi_identification_code', 'branch_identification_code'], 'branch_collateral_credit_gaurar_fk');
            $table->primary(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'borrower_account_reference']);
        });

        Schema::connection('mysql')->create('collateral_material_collateral', function (Blueprint $table) {
            $table->char('pi_identification_code', 6)->index('collateral_material_collateral_piw_indx');
            $table->char('branch_identification_code', 3)->index('cmc_brcodew_indx');
            $table->string('borrowers_client_number', 30)->index('cmc_clientw_indx');
            $table->string('borrower_account_reference', 30)->index('cmc_borrow_accounntw_ref_indx');
            $table->integer('borrower_classification');
            $table->integer('collateral_type_identification');
            $table->string('collateral_reference_number', 20)->nullable();
            $table->string('collateral_description', 1000);
            $table->char('collateral_currency', 3);
            $table->decimal('collateral_open_market_value', 21);
            $table->decimal('collateral_forced_sale_value', 21);
            $table->date('collateral_valuation_expiry_date');
            $table->string('instrument_of_claim', 30);
            $table->date('valuation_date');
            $table->date('submission_Date')->nullable();

            $table->index(['pi_identification_code', 'borrowers_client_number', 'borrower_account_reference'], 'collateral_material__index');
            $table->primary(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'borrower_account_reference']);
        });

        Schema::connection('mysql')->create('credit_applications', function (Blueprint $table) {
            $table->char('pi_identification_code', 6)->index('credit_applicat_pi_indxd');
            $table->char('branch_identification_code', 3)->index('credit_applicat_brcode_indxd');
            $table->string('client_number', 30)->nullable()->index('cap_clientn_indxd');
            $table->string('credit_application_reference', 30)->index('app_application_indxd');
            $table->integer('applicant_classification');
            $table->date('credit_application_date');
            $table->string('amount', 40);
            $table->char('currency', 3);
            $table->integer('credit_account_or_loan_product_type');
            $table->integer('credit_application_status');
            $table->date('last_status_change_date');
            $table->integer('credit_application_duration');
            $table->integer('rejection_reason')->nullable();
            $table->char('client_consent_flag', 1);
            $table->string('group_identification_joint_account_number', 30)->nullable();
            $table->integer('individual_id')->nullable()->index('credit_applicat_ind_indxd');
            $table->integer('non_individual_id')->nullable()->index('credit_applicat_non_ind_indxd');
            $table->date('submission_date')->nullable();
            $table->string('amount_approved', 40)->nullable();
            $table->string('approved_currency', 25)->nullable();

            $table->index(['pi_identification_code', 'branch_identification_code', 'client_number', 'credit_application_reference'], 'credit_applicat_idxd');
            $table->primary(['pi_identification_code', 'branch_identification_code', 'credit_application_reference']);
        });

        Schema::connection('mysql')->create('credit_borrower_account', function (Blueprint $table) {
            $table->char('pi_identification_code', 6)->index('credit_borrower_pi_indx');
            $table->char('branch_identification_code', 3)->index('credit_borrower_account_brcode_indx');
            $table->string('borrowers_client_number', 30)->index('cba_client_indx');
            $table->integer('borrower_classification');
            $table->string('credit_account_reference', 30)->index('cba_borrow_accounnt_ref_indx');
            $table->date('credit_account_date')->index('credit_account_date_idx');
            $table->string('currency', 5)->nullable();
            $table->decimal('credit_amount', 21)->nullable();
            $table->decimal('credit_amount_ugx_equivalent', 21)->nullable();
            $table->decimal('facility_amount_granted', 21)->nullable();
            $table->decimal('credit_amount_drawdown', 21)->nullable();
            $table->decimal('credit_amount_drawdown_ugx_equivalent', 21)->nullable();
            $table->integer('credit_account_type');
            $table->string('group_identification_joint_account_number', 30)->nullable();
            $table->date('maturity_date');
            $table->integer('type_of_interest')->nullable();
            $table->integer('interest_calculation_method')->nullable();
            $table->decimal('annual_interest_rate_at_disbursement', 7, 4)->nullable();
            $table->date('date_of_first_payment')->nullable();
            $table->integer('credit_amortization_type');
            $table->integer('credit_payment_frequency')->nullable();
            $table->integer('number_of_payments')->nullable();
            $table->decimal('monthly_instalment_amount', 21)->nullable();
            $table->decimal('specific_provision_amount', 21)->nullable();
            $table->char('client_consent_flag', 1)->nullable();
            $table->char('client_advice_notice_flag', 1)->nullable();
            $table->integer('term')->nullable();
            $table->integer('loan_purpose')->nullable();
            $table->decimal('group_joint_account_exposure_amount', 21)->nullable();
            $table->integer('flag_for_group')->nullable();
            $table->integer('flag_for_joint_account')->nullable();
            $table->integer('individual_id')->nullable()->index('credit_borrower_account_ind_indx');
            $table->integer('nonindividual_id')->nullable()->index('nonindividual_credit_account_reference_fk');
            $table->date('submission_date')->nullable()->index('credit_borrower_acc_sub_idx');
            $table->string('old_account_number', 100)->nullable();
            $table->char('old_pi_identification_code', 6)->nullable();
            $table->string('old_branch_code', 20)->nullable();
            $table->string('old_client_number', 100)->nullable();

            $table->index(['pi_identification_code', 'borrowers_client_number', 'credit_account_reference'], 'credit_borrower_acco_indx');
            $table->index(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference'], 'credit_borrower_account_indx');
            $table->index(['borrowers_client_number'], 'currency_table_ind5');
            $table->index(['credit_account_reference'], 'currency_table_ind6');
            $table->index(['submission_date'], 'currency_table_ind7');
            $table->primary(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference']);
        });

        Schema::connection('mysql')->create('credit_borrower_account_repayment', function (Blueprint $table) {
            $table->char('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->string('borrowers_client_number', 30);
            $table->string('credit_account_reference', 30)->index('acc_repayment_0718');
            $table->date('transaction_date')->nullable();
            $table->char('currency', 3)->nullable();
            $table->integer('opening_balance_indicator')->nullable();
            $table->decimal('annual_interest_rate_at_reporting', 10)->nullable();
            $table->decimal('current_balance_amount', 21)->nullable();
            $table->decimal('current_balance_amount_ugx_equivalent', 21)->nullable();
            $table->integer('current_balance_indicator')->nullable();
            $table->date('last_payment_date')->nullable();
            $table->decimal('last_payment_amount', 21)->nullable();
            $table->integer('credit_account_status')->nullable();
            $table->date('last_status_change_date')->nullable();
            $table->integer('credit_account_risk_classification')->nullable();
            $table->date('credit_account_arrears_date')->nullable();
            $table->integer('number_of_days_in_arrears')->nullable();
            $table->decimal('balance_overdue', 21)->nullable();
            $table->integer('flag_for_restructured_credit')->nullable();
            $table->integer('balance_overdue_indicator')->nullable();
            $table->date('credit_account_closure_date')->nullable();
            $table->integer('credit_account_closure_reason')->nullable();
            $table->date('submission_date');
            $table->string('mode_of_restructure', 100)->nullable();
            $table->string('risk_classification_criteria', 100)->nullable();

            $table->index(['pi_identification_code', 'branch_identification_code', 'credit_account_reference', 'borrowers_client_number', 'transaction_date'], 'cba_unique_r_idx2');
            $table->index(['submission_date', 'pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference'], 'cba_unique_sub_r_idx2');
            $table->index(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference'], 'credit_borrower_account_fk');
            $table->primary(['submission_date', 'pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference']);
        });

        Schema::connection('mysql')->create('employment_info', function (Blueprint $table) {
            $table->integer('employment_info_id', true);
            $table->integer('individual_id')->nullable()->index('individual_employement_info_fk');
            $table->integer('employment_type')->nullable();
            $table->string('primary_occupation', 100)->nullable();
            $table->string('employer_name', 200)->nullable();
            $table->string('employee_number', 150)->nullable();
            $table->date('employment_date')->nullable();
            $table->integer('income_band')->nullable();
            $table->integer('salary_frequency')->nullable();
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('failed_jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });

        Schema::connection('mysql')->create('financial_malpractice', function (Blueprint $table) {
            $table->string('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->string('client_number', 30);
            $table->integer('consumer_classification')->nullable();
            $table->integer('category_code')->nullable();
            $table->integer('sub_category_code')->nullable();
            $table->date('incident_date')->nullable();
            $table->decimal('loss_amount', 21)->nullable();
            $table->char('currency_type', 3)->nullable();
            $table->string('incident_details', 1000)->nullable();
            $table->char('forensic_information_available', 1)->nullable();
            $table->integer('individual_id')->nullable()->index('individual_financial_mulpractice_fk');
            $table->integer('non_individual_id')->nullable()->index('nonindividual_financial_mulpractice_fk');
            $table->date('submission_date');

            $table->index(['pi_identification_code', 'branch_identification_code'], 'branch_financial_mulpractice_fk');
            $table->primary(['submission_date', 'pi_identification_code', 'branch_identification_code', 'client_number']);
        });

        Schema::connection('mysql')->create('individual_addresses', function (Blueprint $table) {
            $table->integer('addresses_id', true);
            $table->integer('individual_id')->index('individual_individual_addresses_fk');
            $table->string('unit_number', 10)->nullable();
            $table->string('building_name', 50)->nullable();
            $table->string('floor_number', 30)->nullable();
            $table->string('plot_or_street_number', 10)->nullable();
            $table->string('lc_or_street_name', 50)->nullable();
            $table->string('parish', 50)->nullable();
            $table->string('suburb', 50)->nullable();
            $table->string('village', 50)->nullable();
            $table->string('county_or_town', 50)->nullable();
            $table->string('district', 50)->nullable();
            $table->integer('region')->nullable();
            $table->string('po_box_number', 10)->nullable();
            $table->string('post_office_town', 20)->nullable();
            $table->char('country_code', 2)->nullable();
            $table->integer('period_at_address')->nullable();
            $table->char('flag_of_ownership', 1)->nullable();
            $table->string('email_address', 150)->nullable();
            $table->string('web_site', 50)->nullable();
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('individual_addresses_sec', function (Blueprint $table) {
            $table->integer('addresses_id', true);
            $table->integer('individual_id')->index('individual_individual_addresses_sec_fk');
            $table->string('unit_number', 20)->nullable();
            $table->string('building_name', 50)->nullable();
            $table->string('floor_number', 30)->nullable();
            $table->string('plot_or_street_number', 10)->nullable();
            $table->string('lc_or_street_name', 50)->nullable();
            $table->string('parish', 50)->nullable();
            $table->string('suburb', 50)->nullable();
            $table->string('village', 50)->nullable();
            $table->string('county_or_town', 50)->nullable();
            $table->string('district', 50)->nullable();
            $table->integer('region')->nullable();
            $table->string('po_box_number', 10)->nullable();
            $table->string('post_office_town', 20)->nullable();
            $table->char('country_code', 2)->nullable();
            $table->integer('period_at_address')->nullable();
            $table->char('flag_of_ownership', 1)->nullable();
            $table->string('email_address', 150)->nullable();
            $table->string('web_site', 50)->nullable();
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('individual_fcs_numbers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->longText('image');
            $table->string('fcs_number', 15)->unique();
            $table->string('gscafb_forename_1', 100);
            $table->string('gscafb_forename_2', 100)->nullable();
            $table->string('gscafb_surname', 100);
            $table->string('gscafb_forename_3', 100)->nullable();
            $table->string('gscafb_alias', 100)->nullable();
            $table->date('gscafb_date_of_birth')->nullable();
            $table->string('gscafb_gender')->nullable();
            $table->string('ei_primary_occupation', 100)->nullable();
            $table->string('ei_employer', 100)->nullable();
            $table->string('ei_employee_no', 100)->nullable();
            $table->string('ei_employment_type', 50)->nullable();
            $table->integer('ei_period_employed')->nullable();
            $table->string('updf_emp_number', 20)->nullable();
            $table->string('prison_id_number', 20)->nullable();
            $table->string('ii_nssf', 20)->nullable();
            $table->string('ii_tin', 20)->nullable();
            $table->string('new_tin_number', 20)->nullable();
            $table->string('ii_passport_no')->nullable();
            $table->string('ii_voters_perno', 20)->nullable();
            $table->string('ii_drivers_lic', 20)->nullable();
            $table->string('ii_drivers_permit', 20)->nullable();
            $table->string('lc_id_card_number', 20)->nullable();
            $table->string('country_id', 20)->nullable()->index('individual_fcs_numbers_ctry_i_index');
            $table->string('police_id_number', 50)->nullable();
            $table->string('teacher_registration_number', 50)->nullable();
            $table->string('pension_number', 50)->nullable();
            $table->string('country_code', 3)->nullable();
            $table->string('region', 50)->nullable();
            $table->string('district', 50)->nullable();
            $table->string('town', 50)->nullable();
            $table->string('suburb', 50)->nullable();
            $table->string('parish', 50)->nullable();
            $table->string('street_name', 50)->nullable();
            $table->string('street_no', 10)->nullable();
            $table->string('floor_no', 10)->nullable();
            $table->string('unit_name', 50)->nullable();
            $table->string('unit_no', 10)->nullable();
            $table->integer('period_at_addr')->nullable();
            $table->string('pri_tel_no_country_code_id', 10)->nullable();
            $table->string('pri_tel_no_area_code', 5)->nullable();
            $table->string('pri_tel_no', 15)->nullable();
            $table->string('mob_tel_no_country_code_id', 10)->nullable();
            $table->string('mob_tel_no_area_code', 5)->nullable();
            $table->string('mob_tel_no', 15)->nullable();
            $table->string('oth_tel_no_country_code_id', 10)->nullable();
            $table->string('oth_tel_no_area_code', 5)->nullable();
            $table->string('oth_tel_no', 15)->nullable();
            $table->string('fax_tel_no_country_code_id', 10)->nullable();
            $table->string('fax_tel_no_area_code', 5)->nullable();
            $table->string('fax_tel_no', 15)->nullable();
            $table->string('email_address', 100)->nullable();
            $table->string('ownership', 10)->nullable();
            $table->string('postal_addr_no', 15)->nullable();
            $table->string('post_office', 50)->nullable();
            $table->string('employers_country_code', 5)->nullable();
            $table->string('employers_region', 50)->nullable();
            $table->string('employers_district', 50)->nullable();
            $table->string('employers_town', 50)->nullable();
            $table->string('employers_suburb', 50)->nullable();
            $table->string('employers_parish', 50)->nullable();
            $table->string('employers_street_name', 50)->nullable();
            $table->string('employers_street_no', 15)->nullable();
            $table->string('employers_floor_no', 10)->nullable();
            $table->string('employers_unit_name', 50)->nullable();
            $table->string('employers_unit_no', 15)->nullable();
            $table->string('emp_pri_tel_no_country_code_id', 10)->nullable();
            $table->string('employers_pri_tel_no_area_code', 5)->nullable();
            $table->string('employers_pri_tel_no', 15)->nullable();
            $table->string('emp_mob_tel_no_country_code_id', 10)->nullable();
            $table->string('emp_mob_tel_no_area_code', 5)->nullable();
            $table->string('employers_mob_tel_no', 15)->nullable();
            $table->string('emp_oth_tel_no_country_code_id', 10)->nullable();
            $table->string('emp_oth_tel_no_area_code', 5)->nullable();
            $table->string('employers_oth_tel_no', 15)->nullable();
            $table->string('emp_fax_tel_no_country_code_id', 10)->nullable();
            $table->string('emp_fax_tel_no_area_code', 5)->nullable();
            $table->string('employers_fax_tel_no', 15)->nullable();
            $table->string('employers_email_address', 100)->nullable();
            $table->string('employers_ownership', 50)->nullable();
            $table->text('employers_postal_addr_no')->nullable();
            $table->text('employers_post_office')->nullable();
            $table->date('date_created')->nullable();
            $table->string('institution name', 100)->nullable();
            $table->string('branch name', 100)->nullable();
            $table->string('status', 50)->nullable();
            $table->timestamps();
        });

        Schema::connection('mysql')->create('individual_identifiers', function (Blueprint $table) {
            $table->integer('individual_id')->index('individual_id_index');
            $table->string('identifier', 20);
            $table->string('identifier_type', 50);
            $table->char('country_issuing_authority', 2)->nullable();
            $table->char('country_of_issue', 2)->nullable()->default('UG');
            $table->date('submission_date')->nullable();

            $table->index(['identifier', 'identifier_type'], 'individual_identifiers_idxu');
            $table->primary(['identifier', 'identifier_type']);
        });

        Schema::connection('mysql')->create('individual_telephone_numbers', function (Blueprint $table) {
            $table->integer('telephone_number_id', true);
            $table->integer('individual_id')->index('individual_telephonenumbers_fk');
            $table->integer('primary_number_country_dialling_code')->nullable();
            $table->bigInteger('primary_number_telephone_number')->nullable();
            $table->integer('other_number_country_dialling_code')->nullable();
            $table->bigInteger('other_number_telephone_number')->nullable();
            $table->integer('mobile_number_country_dialling_code')->nullable();
            $table->bigInteger('mobile_number_telephone_number')->nullable();
            $table->integer('facsimile_country_dialling_code')->nullable();
            $table->bigInteger('facsimile_number')->nullable();
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('individual_telephone_numbers_sec', function (Blueprint $table) {
            $table->integer('telephone_number_id', true);
            $table->integer('individual_id')->index('individual_telephone_numbers_sec_indx');
            $table->integer('primary_number_country_dialling_code')->nullable();
            $table->bigInteger('primary_number_telephone_number')->nullable();
            $table->integer('other_number_country_dialling_code')->nullable();
            $table->bigInteger('other_number_telephone_number')->nullable();
            $table->integer('mobile_number_country_dialling_code')->nullable();
            $table->bigInteger('mobile_number_telephone_number')->nullable();
            $table->integer('facsimile_country_dialling_code')->nullable();
            $table->bigInteger('facsimile_number')->nullable();
            $table->date('submission_date')->nullable();

            $table->index(['individual_id'], 'individual_telephonenumbers_sec_fk');
        });

        Schema::connection('mysql')->create('individuals', function (Blueprint $table) {
            $table->integer('individual_id', true)->index('individuals_idx');
            $table->string('surname', 100)->nullable();
            $table->string('forename1', 100)->nullable();
            $table->string('forename2', 100)->nullable();
            $table->string('forename3', 100)->nullable();
            $table->char('gender', 1)->nullable();
            $table->char('marital_status', 2)->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('nationality', 20)->nullable();
            $table->date('submission_date')->nullable();

            $table->primary(['individual_id']);
        });

        Schema::connection('mysql')->create('institution_addresses', function (Blueprint $table) {
            $table->integer('addresses_id', true);
            $table->char('pi_identification_code', 6)->index('institutional_addresses_fk');
            $table->string('pci_unit_number', 10)->nullable();
            $table->string('pci_building_name', 50)->nullable();
            $table->string('pci_floor_number', 30)->nullable();
            $table->string('pci_plot_or_street_number', 10)->nullable();
            $table->string('pci_lc_or_street_name', 50)->nullable();
            $table->string('pci_parish', 50)->nullable();
            $table->string('pci_suburb', 50)->nullable();
            $table->string('pci_village', 50)->nullable();
            $table->string('pci_county_or_town', 50)->nullable();
            $table->string('pci_district', 50)->nullable();
            $table->integer('pci_region')->nullable();
            $table->string('pci_po_box_number', 10)->nullable();
            $table->string('pci_post_office_town', 20)->nullable();
            $table->char('pci_country_code', 2)->nullable();
            $table->integer('pci_period_at_address')->nullable();
            $table->char('pci_flag_of_ownership', 1)->nullable();
            $table->string('pci_email_address', 150)->nullable();
            $table->string('pci_web_site', 50)->nullable();
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('institution_telephone_numbers', function (Blueprint $table) {
            $table->integer('telephone_number_id', true);
            $table->char('pi_identification_code', 6)->index('institution_telephonenumbers_fk');
            $table->integer('pci_primary_number_country_dialling_code')->nullable();
            $table->integer('pci_primary_number_telephone_number')->nullable();
            $table->integer('pci_other_number_country_dialling_code')->nullable();
            $table->integer('pci_other_number_telephone_number')->nullable();
            $table->integer('pci_mobile_number_country_dialling_code')->nullable();
            $table->integer('pci_mobile_number_telephone_number')->nullable();
            $table->integer('pci_facsimile_country_dialling_code')->nullable();
            $table->integer('pci_facsimile_number')->nullable();
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('non_individual_addresses', function (Blueprint $table) {
            $table->integer('addresses_id', true);
            $table->integer('non_individual_id')->index('non_individual_addresses_inx');
            $table->string('unit_number', 10)->nullable();
            $table->string('building_name', 50)->nullable();
            $table->string('floor_number', 30)->nullable();
            $table->string('plot_or_street_number', 10)->nullable();
            $table->string('lc_or_street_name', 50)->nullable();
            $table->string('parish', 50)->nullable();
            $table->string('suburb', 50)->nullable();
            $table->string('village', 50)->nullable();
            $table->string('county_or_town', 50)->nullable();
            $table->string('district', 50)->nullable();
            $table->integer('region')->nullable();
            $table->string('po_box_number', 10)->nullable();
            $table->string('post_office_town', 20)->nullable();
            $table->char('country_code', 2)->nullable();
            $table->integer('period_at_address')->nullable();
            $table->char('flag_of_ownership', 1)->nullable();
            $table->string('email_address', 150)->nullable();
            $table->string('web_site', 50)->nullable();
            $table->date('submission_date')->nullable();

            $table->index(['non_individual_id'], 'nonindividual_addresses_fk');
        });

        Schema::connection('mysql')->create('non_individual_addresses_sec', function (Blueprint $table) {
            $table->integer('addresses_id', true);
            $table->integer('non_individual_id')->index('non_individual_addresses_sec_inx');
            $table->string('unit_number', 10)->nullable();
            $table->string('building_name', 50)->nullable();
            $table->string('floor_number', 30)->nullable();
            $table->string('plot_or_street_number', 10)->nullable();
            $table->string('lc_or_street_name', 50)->nullable();
            $table->string('parish', 50)->nullable();
            $table->string('suburb', 50)->nullable();
            $table->string('village', 50)->nullable();
            $table->string('county_or_town', 50)->nullable();
            $table->string('district', 50)->nullable();
            $table->integer('region')->nullable();
            $table->string('po_box_number', 10)->nullable();
            $table->string('post_office_town', 20)->nullable();
            $table->char('country_code', 2)->nullable();
            $table->integer('period_at_address')->nullable();
            $table->char('flag_of_ownership', 1)->nullable();
            $table->string('email_address', 150)->nullable();
            $table->string('web_site', 50)->nullable();
            $table->date('submission_date')->nullable();

            $table->index(['non_individual_id'], 'nonindividual_addresses_sec_fk');
        });

        Schema::connection('mysql')->create('non_individual_fcs_numbers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->longText('agent_fcs_image')->nullable();
            $table->string('nib_fcs_number')->nullable()->unique();
            $table->string('gscafb_business_name', 100)->nullable();
            $table->string('gscafb_trading_name', 100)->nullable();
            $table->date('gscafb_date_registered')->nullable();
            $table->text('gscafb_activity_description')->nullable();
            $table->string('business_type')->nullable();
            $table->string('industry_sector type')->nullable();
            $table->string('ii_tin')->nullable();
            $table->string('new_tin_number')->nullable();
            $table->string('ii_reg_cert_no')->nullable();
            $table->string('ii_uia_no')->nullable();
            $table->string('ii_vat_no')->nullable();
            $table->string('comp_kacita_num')->nullable();
            $table->string('church_id')->nullable();
            $table->string('mosque_id')->nullable();
            $table->string('dioceses_id')->nullable();
            $table->string('local_government_id')->nullable();
            $table->string('schools_id')->nullable();
            $table->string('sacco_mof_id')->nullable();
            $table->string('savings_and_coop_societies_id')->nullable();
            $table->string('town_council_id')->nullable();
            $table->string('country_code')->nullable();
            $table->string('region')->nullable();
            $table->string('district')->nullable();
            $table->string('town')->nullable();
            $table->string('suburb')->nullable();
            $table->string('parish')->nullable();
            $table->string('street_name')->nullable();
            $table->string('street_no')->nullable();
            $table->string('floor_no')->nullable();
            $table->string('unit_name')->nullable();
            $table->string('unit_no')->nullable();
            $table->string('period_at_addr')->nullable();
            $table->string('pri_tel_no_country_code_id')->nullable();
            $table->string('pri_tel_no_area_code')->nullable();
            $table->string('pri_tel_no')->nullable();
            $table->string('mob_tel_no_country_code_id')->nullable();
            $table->string('mob_tel_no_area_code')->nullable();
            $table->string('mob_tel_no')->nullable();
            $table->string('oth_tel_no_country_code_id')->nullable();
            $table->string('oth_tel_no_area_code')->nullable();
            $table->string('oth_tel_no')->nullable();
            $table->string('fax_tel_no_country_code_id')->nullable();
            $table->string('fax_tel_no_area_code')->nullable();
            $table->string('fax_tel_no')->nullable();
            $table->string('email_address')->nullable();
            $table->string('ownership')->nullable();
            $table->string('postal_addr_no')->nullable();
            $table->string('post_offices_id')->nullable();
            $table->date('date_created')->nullable();
            $table->string('institution_name')->nullable();
            $table->string('branch_name')->nullable();
            $table->string('status', 50)->nullable();
            $table->timestamps();
        });

        Schema::connection('mysql')->create('non_individual_identifiers', function (Blueprint $table) {
            $table->integer('non_individual_id')->nullable()->index('non_individual_identifiers_fk');
            $table->string('identifier', 20);
            $table->string('identifier_type', 50);
            $table->char('country_issuing_authority', 2)->nullable();
            $table->string('nationality', 20)->nullable();
            $table->char('country_of_issue', 2)->nullable()->default('UG');
            $table->date('submission_date')->nullable();

            $table->index(['identifier', 'identifier_type'], 'nonindividual_identifiers_idxu');
            $table->primary(['identifier', 'identifier_type']);
        });

        Schema::connection('mysql')->create('non_individual_telephone_numbers', function (Blueprint $table) {
            $table->integer('telephone_number_id', true);
            $table->integer('non_individual_id')->nullable()->index('non_individual_telephone_numbers_idx');
            $table->integer('primary_number_country_dialling_code')->nullable();
            $table->bigInteger('primary_number_telephone_number')->nullable();
            $table->integer('other_number_country_dialling_code')->nullable();
            $table->bigInteger('other_number_telephone_number')->nullable();
            $table->integer('mobile_number_country_dialling_code')->nullable();
            $table->bigInteger('mobile_number_telephone_number')->nullable();
            $table->integer('facsimile_country_dialling_code')->nullable();
            $table->bigInteger('facsimile_number')->nullable();
            $table->date('submission_date')->nullable();

            $table->index(['non_individual_id'], 'nonindividual_telephonenumber_fk');
        });

        Schema::connection('mysql')->create('non_individual_telephone_numbers_sec', function (Blueprint $table) {
            $table->integer('telephone_number_id', true);
            $table->integer('non_individual_id')->nullable()->index('non_individual_telephone_numbers_sec_idx');
            $table->integer('primary_number_country_dialling_code')->nullable();
            $table->bigInteger('primary_number_telephone_number')->nullable();
            $table->integer('other_number_country_dialling_code')->nullable();
            $table->bigInteger('other_number_telephone_number')->nullable();
            $table->integer('mobile_number_country_dialling_code')->nullable();
            $table->bigInteger('mobile_number_telephone_number')->nullable();
            $table->integer('facsimile_country_dialling_code')->nullable();
            $table->bigInteger('facsimile_number')->nullable();
            $table->date('submission_date')->nullable();

            $table->index(['non_individual_id'], 'nonindividual_telephonenumber_sec_fk');
        });

        Schema::connection('mysql')->create('non_individuals', function (Blueprint $table) {
            $table->integer('non_individual_id', true)->index('non_individuals_idx');
            $table->string('business_name', 100)->nullable();
            $table->string('trading_name', 100)->nullable();
            $table->string('activity_description', 100)->nullable();
            $table->integer('industry_sector_code')->nullable();
            $table->date('date_registered')->nullable();
            $table->integer('business_type_code')->nullable();
            $table->date('submission_date')->nullable();

            $table->primary(['non_individual_id']);
        });

        Schema::connection('mysql')->create('online_credit_application', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('submission_date', 100)->nullable();
            $table->string('pi_identification_code', 100)->nullable();
            $table->string('branch_identification_code', 100)->nullable();
            $table->string('client_number', 100)->nullable();
            $table->string('credit_application_reference', 100)->nullable();
            $table->string('applicant_classification', 100)->nullable();
            $table->string('credit_application_date', 100)->nullable();
            $table->string('amount', 100)->nullable();
            $table->string('approved', 100)->nullable();
            $table->string('currency', 100)->nullable();
            $table->string('credit_account_or_loan_product_type', 100)->nullable();
            $table->string('credit_application_status', 100)->nullable();
            $table->string('last_status_change_date', 100)->nullable();
            $table->string('credit_application_duration', 100)->nullable();
            $table->string('rejection_reason', 100)->nullable();
            $table->string('client_consent_flag', 100)->nullable();
            $table->string('group_identification_joint_account_number', 100)->nullable();
            $table->string('ii_registration_certificate_number', 100)->nullable();
            $table->string('ii_tax_identification_number', 100)->nullable();
            $table->string('ii_fcs_number', 100)->nullable();
            $table->string('ii_passport_number', 100)->nullable();
            $table->string('ii_country_id', 100)->nullable();
            $table->string('ii_country_issuing_authority', 100)->nullable();
            $table->string('ii_nationality', 100)->nullable();
            $table->string('gscafb_business_name', 100)->nullable();
            $table->string('gscafb_trading_name', 100)->nullable();
            $table->string('gscafb_activity_description', 100)->nullable();
            $table->string('gscafb_industry_sector_code', 100)->nullable();
            $table->string('gscafb_date_registered', 100)->nullable();
            $table->string('gscafb_business_type_code', 100)->nullable();
            $table->string('gscafb_surname', 100)->nullable();
            $table->string('gscafb_forename1', 100)->nullable();
            $table->string('gscafb_forename2', 100)->nullable();
            $table->string('gscafb_forename3', 100)->nullable();
            $table->string('gscafb_gender', 100)->nullable();
            $table->string('gscafb_marital_status', 100)->nullable();
            $table->string('gscafb_date_of_birth', 100)->nullable();
            $table->string('ei_employment_type', 100)->nullable();
            $table->string('ei_primary_occupation', 100)->nullable();
            $table->string('ei_employer_name', 100)->nullable();
            $table->string('ei_employee_number', 100)->nullable();
            $table->string('ei_employment_date', 100)->nullable();
            $table->string('ei_income_band', 100)->nullable();
            $table->string('ei_salary_frequency', 100)->nullable();
            $table->string('pci_unit_number', 100)->nullable();
            $table->string('pci_building_name', 100)->nullable();
            $table->string('pci_floor_number', 100)->nullable();
            $table->string('pci_plot_or_street_number', 100)->nullable();
            $table->string('pci_lc_or_street_name', 100)->nullable();
            $table->string('pci_parish', 100)->nullable();
            $table->string('pci_suburb', 100)->nullable();
            $table->string('pci_village', 100)->nullable();
            $table->string('pci_county_or_town', 100)->nullable();
            $table->string('pci_district', 100)->nullable();
            $table->string('pci_region', 100)->nullable();
            $table->string('pci_po_box_number', 100)->nullable();
            $table->string('pci_post_office_town', 100)->nullable();
            $table->string('pci_country_code', 100)->nullable();
            $table->string('pci_period_at_address', 100)->nullable();
            $table->string('pci_flag_of_ownership', 100)->nullable();
            $table->string('pci_primary_number_country_dialling_code', 100)->nullable();
            $table->string('pci_primary_number_telephone_number', 100)->nullable();
            $table->string('pci_other_number_country_dialling_code', 100)->nullable();
            $table->string('pci_other_number_telephone_number', 100)->nullable();
            $table->string('pci_mobile_number_country_dialling_code', 100)->nullable();
            $table->string('pci_mobile_number_telephone_number', 100)->nullable();
            $table->string('pci_facsimile_country_dialling_code', 100)->nullable();
            $table->string('pci_facsimile_number', 100)->nullable();
            $table->string('pci_email_address', 100)->nullable();
            $table->string('pci_web_site', 100)->nullable();
            $table->integer('downloadstatus')->default(0);
            $table->string('createdby', 100)->nullable();
            $table->dateTime('datecreated')->nullable()->useCurrent();
            $table->string('modifiedby', 100)->nullable();
            $table->dateTime('datemodified')->nullable()->useCurrent();
        });

        Schema::connection('mysql')->create('pi_information', function (Blueprint $table) {
            $table->char('pi_identification_code', 6)->primary();
            $table->char('institution_type', 3)->nullable();
            $table->string('institution_name', 100)->nullable();
            $table->date('license_issuing_date')->nullable();
            $table->date('submission_date')->nullable();
            $table->string('license_number', 50)->nullable();
        });

        Schema::connection('mysql')->create('pi_stakeholders', function (Blueprint $table) {
            $table->integer('pi_stakeholder_id', true);
            $table->string('pi_identification_code', 6)->nullable()->index('pi_institutional_stakeholders_fk');
            $table->decimal('shareholder_percentage', 5)->nullable();
            $table->integer('stakeholder_type')->nullable();
            $table->integer('stakeholder_category')->nullable();
            $table->integer('individual_id')->nullable()->index('pi_stakeholders_id_fk');
            $table->integer('non_individual_id')->nullable()->index('pi_stakeholders_nid_fk');
            $table->date('submission_date')->nullable();
        });

        Schema::connection('mysql')->create('repayment_summary', function (Blueprint $table) {
            $table->date('submission_date')->nullable();
            $table->char('pi_identification_code', 6);
            $table->char('branch_identification_code', 3);
            $table->string('borrowers_client_number', 30);
            $table->string('credit_account_reference', 30);
            $table->integer('borrower_id')->nullable();
        });

        Schema::connection('mysql')->table('borrower_stakeholders', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'borrower_stakeholders_idv__fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['non_individual_id'], 'borrower_stakeholders_nidv__fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('bounced_cheques', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'bounced_cheques_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['nonindividual_id'], 'bounced_cheques_nonid_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('branch_addresses', function (Blueprint $table) {
            $table->foreign(['pi_identification_code', 'branch_identification_code'], 'branch_branch_addresses_fk')->references(['pi_identification_code', 'branch_identification_code'])->on('branch_information')->onUpdate('cascade')->onDelete('no action');
        });

        Schema::connection('mysql')->table('branch_telephone_numbers', function (Blueprint $table) {
            $table->foreign(['pi_identification_code', 'branch_identification_code'], 'branch_branch_telephonenumbers_fk')->references(['pi_identification_code', 'branch_identification_code'])->on('branch_information')->onUpdate('cascade')->onDelete('no action');
        });

        Schema::connection('mysql')->table('collateral_credit_guarantor', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'ccg_indiviidual_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['non_individual_id'], 'ccg_nonindiviidual_id_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('collateral_material_collateral', function (Blueprint $table) {
            $table->foreign(['pi_identification_code', 'borrowers_client_number', 'borrower_account_reference'], 'cba_fx')->references(['pi_identification_code', 'borrowers_client_number', 'credit_account_reference'])->on('credit_borrower_account')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('credit_applications', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'cap_fk_individual')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['non_individual_id'], 'cap_fk_nonindividual')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('credit_borrower_account', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'cba_fk_individual')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['nonindividual_id'], 'cba_fk_nonindividual')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('credit_borrower_account_repayment', function (Blueprint $table) {
            $table->foreign(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference'], 'credit_borrower_account_fk')->references(['pi_identification_code', 'branch_identification_code', 'borrowers_client_number', 'credit_account_reference'])->on('credit_borrower_account')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('employment_info', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'employment_info_idv_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('financial_malpractice', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'individual_financial_mulpractice_fk')->references(['individual_id'])->on('individuals')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['non_individual_id'], 'nonindividual_financial_mulpractice_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('cascade')->onDelete('no action');
        });

        Schema::connection('mysql')->table('individual_addresses', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'individual_addresses_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('individual_addresses_sec', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'individual_addresses_sec_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('individual_identifiers', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'individual_identifiers_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('individual_telephone_numbers', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'individual_telephone_numbers_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('individual_telephone_numbers_sec', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'individual_telephonenumbers_sec_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('institution_addresses', function (Blueprint $table) {
            $table->foreign(['pi_identification_code'], 'institutional_addresses_fk')->references(['pi_identification_code'])->on('pi_information')->onUpdate('no action')->onDelete('no action');
        });

        Schema::connection('mysql')->table('institution_telephone_numbers', function (Blueprint $table) {
            $table->foreign(['pi_identification_code'], 'institution_telephonenumbers_fk')->references(['pi_identification_code'])->on('pi_information')->onUpdate('no action')->onDelete('no action');
        });

        Schema::connection('mysql')->table('non_individual_addresses', function (Blueprint $table) {
            $table->foreign(['non_individual_id'], 'non_individual_addresses_id_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['non_individual_id'], 'nonindividual_addresses_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('no action');
        });

        Schema::connection('mysql')->table('non_individual_addresses_sec', function (Blueprint $table) {
            $table->foreign(['non_individual_id'], 'non_individual_addresses_sec_id_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('non_individual_identifiers', function (Blueprint $table) {
            $table->foreign(['non_individual_id'], 'non_individual_identifiers_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('non_individual_telephone_numbers', function (Blueprint $table) {
            $table->foreign(['non_individual_id'], 'non_individual_telephone_numbers_id_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('non_individual_telephone_numbers_sec', function (Blueprint $table) {
            $table->foreign(['non_individual_id'], 'nonindividual_telephonenumber_sec_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });

        Schema::connection('mysql')->table('pi_stakeholders', function (Blueprint $table) {
            $table->foreign(['individual_id'], 'pi_stakeholders_id_fk')->references(['individual_id'])->on('individuals')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['non_individual_id'], 'pi_stakeholders_nid_fk')->references(['non_individual_id'])->on('non_individuals')->onUpdate('no action')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql')->table('pi_stakeholders', function (Blueprint $table) {
            $table->dropForeign('pi_stakeholders_id_fk');
            $table->dropForeign('pi_stakeholders_nid_fk');
        });

        Schema::connection('mysql')->table('non_individual_telephone_numbers_sec', function (Blueprint $table) {
            $table->dropForeign('nonindividual_telephonenumber_sec_fk');
        });

        Schema::connection('mysql')->table('non_individual_telephone_numbers', function (Blueprint $table) {
            $table->dropForeign('non_individual_telephone_numbers_id_fk');
        });

        Schema::connection('mysql')->table('non_individual_identifiers', function (Blueprint $table) {
            $table->dropForeign('non_individual_identifiers_fk');
        });

        Schema::connection('mysql')->table('non_individual_addresses_sec', function (Blueprint $table) {
            $table->dropForeign('non_individual_addresses_sec_id_fk');
        });

        Schema::connection('mysql')->table('non_individual_addresses', function (Blueprint $table) {
            $table->dropForeign('non_individual_addresses_id_fk');
            $table->dropForeign('nonindividual_addresses_fk');
        });

        Schema::connection('mysql')->table('institution_telephone_numbers', function (Blueprint $table) {
            $table->dropForeign('institution_telephonenumbers_fk');
        });

        Schema::connection('mysql')->table('institution_addresses', function (Blueprint $table) {
            $table->dropForeign('institutional_addresses_fk');
        });

        Schema::connection('mysql')->table('individual_telephone_numbers_sec', function (Blueprint $table) {
            $table->dropForeign('individual_telephonenumbers_sec_fk');
        });

        Schema::connection('mysql')->table('individual_telephone_numbers', function (Blueprint $table) {
            $table->dropForeign('individual_telephone_numbers_id_fk');
        });

        Schema::connection('mysql')->table('individual_identifiers', function (Blueprint $table) {
            $table->dropForeign('individual_identifiers_id_fk');
        });

        Schema::connection('mysql')->table('individual_addresses_sec', function (Blueprint $table) {
            $table->dropForeign('individual_addresses_sec_id_fk');
        });

        Schema::connection('mysql')->table('individual_addresses', function (Blueprint $table) {
            $table->dropForeign('individual_addresses_id_fk');
        });

        Schema::connection('mysql')->table('financial_malpractice', function (Blueprint $table) {
            $table->dropForeign('individual_financial_mulpractice_fk');
            $table->dropForeign('nonindividual_financial_mulpractice_fk');
        });

        Schema::connection('mysql')->table('employment_info', function (Blueprint $table) {
            $table->dropForeign('employment_info_idv_fk');
        });

        Schema::connection('mysql')->table('credit_borrower_account_repayment', function (Blueprint $table) {
            $table->dropForeign('credit_borrower_account_fk');
        });

        Schema::connection('mysql')->table('credit_borrower_account', function (Blueprint $table) {
            $table->dropForeign('cba_fk_individual');
            $table->dropForeign('cba_fk_nonindividual');
        });

        Schema::connection('mysql')->table('credit_applications', function (Blueprint $table) {
            $table->dropForeign('cap_fk_individual');
            $table->dropForeign('cap_fk_nonindividual');
        });

        Schema::connection('mysql')->table('collateral_material_collateral', function (Blueprint $table) {
            $table->dropForeign('cba_fx');
        });

        Schema::connection('mysql')->table('collateral_credit_guarantor', function (Blueprint $table) {
            $table->dropForeign('ccg_indiviidual_id_fk');
            $table->dropForeign('ccg_nonindiviidual_id_fk');
        });

        Schema::connection('mysql')->table('branch_telephone_numbers', function (Blueprint $table) {
            $table->dropForeign('branch_branch_telephonenumbers_fk');
        });

        Schema::connection('mysql')->table('branch_addresses', function (Blueprint $table) {
            $table->dropForeign('branch_branch_addresses_fk');
        });

        Schema::connection('mysql')->table('bounced_cheques', function (Blueprint $table) {
            $table->dropForeign('bounced_cheques_id_fk');
            $table->dropForeign('bounced_cheques_nonid_fk');
        });

        Schema::connection('mysql')->table('borrower_stakeholders', function (Blueprint $table) {
            $table->dropForeign('borrower_stakeholders_idv__fk');
            $table->dropForeign('borrower_stakeholders_nidv__fk');
        });

        Schema::connection('mysql')->dropIfExists('repayment_summary');

        Schema::connection('mysql')->dropIfExists('pi_stakeholders');

        Schema::connection('mysql')->dropIfExists('pi_information');

        Schema::connection('mysql')->dropIfExists('non_individuals');

        Schema::connection('mysql')->dropIfExists('non_individual_telephone_numbers_sec');

        Schema::connection('mysql')->dropIfExists('non_individual_telephone_numbers');

        Schema::connection('mysql')->dropIfExists('non_individual_identifiers');

        Schema::connection('mysql')->dropIfExists('non_individual_fcs_numbers');

        Schema::connection('mysql')->dropIfExists('non_individual_addresses_sec');

        Schema::connection('mysql')->dropIfExists('non_individual_addresses');

        Schema::connection('mysql')->dropIfExists('institution_telephone_numbers');

        Schema::connection('mysql')->dropIfExists('institution_addresses');

        Schema::connection('mysql')->dropIfExists('individuals');

        Schema::connection('mysql')->dropIfExists('individual_telephone_numbers_sec');

        Schema::connection('mysql')->dropIfExists('individual_telephone_numbers');

        Schema::connection('mysql')->dropIfExists('individual_identifiers');

        Schema::connection('mysql')->dropIfExists('individual_fcs_numbers');

        Schema::connection('mysql')->dropIfExists('individual_addresses_sec');

        Schema::connection('mysql')->dropIfExists('individual_addresses');

        Schema::connection('mysql')->dropIfExists('financial_malpractice');

        Schema::connection('mysql')->dropIfExists('employment_info');

        Schema::connection('mysql')->dropIfExists('credit_borrower_account_repayment');

        Schema::connection('mysql')->dropIfExists('credit_borrower_account');

        Schema::connection('mysql')->dropIfExists('credit_applications');

        Schema::connection('mysql')->dropIfExists('collateral_material_collateral');

        Schema::connection('mysql')->dropIfExists('collateral_credit_guarantor');

        Schema::connection('mysql')->dropIfExists('branch_telephone_numbers');

        Schema::connection('mysql')->dropIfExists('branch_information');

        Schema::connection('mysql')->dropIfExists('branch_addresses');

        Schema::connection('mysql')->dropIfExists('bounced_cheques');

        Schema::connection('mysql')->dropIfExists('borrower_stakeholders');
    }
};
