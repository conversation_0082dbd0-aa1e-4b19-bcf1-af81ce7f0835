<?php

namespace Database\Factories\Validator;

use App\Models\Validator\CreditApplication;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Validator\BorrowerStakeholder>
 */
class CreditApplicationFactory extends Factory
{
    protected $model = CreditApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'submission_date' => '********',
            'pi_identification_code' => 'CB005',
            'branch_identification_code' => '055',
            'client_number' => '2095767',
            'credit_application_reference' => 'OL-************',
            'applicant_classification' => '0',
            'credit_application_date' => '********',
            'amount' => '140000.00',
            'currency' => 'UGX',
            'credit_account_or_loan_product_type' => '14',
            'credit_application_status' => '2',
            'last_status_change_date' => '********',
            'credit_application_duration' => '30',
            'rejection_reason' => null,
            'client_consent_flag' => 'Y',
            'group_identification_joint_account_number' => null,
            'ii_registration_certificate_number' => null,
            'ii_tax_identification_number' => null,
            'ii_value_added_tax_number' => null,
            'ii_fcs_number' => '7485001JFTE1P',
            'ii_passport_number' => null,
            'ii_drivers_licence_id_number' => null,
            'ii_voters_perno' => null,
            'ii_drivers_license_permit_number' => null,
            'ii_nssf_number' => null,
            'ii_country_id' => 'CM690901000C9H',
            'ii_country_issuing_authority' => 'UG',
            'ii_nationality' => 'UG',
            'ii_police_id_number' => null,
            'ii_updf_number' => null,
            'ii_kacita_license_number' => null,
            'ii_public_service_pension_number' => null,
            'ii_teacher_registration_number' => null,
            'ii_country_of_issue' => 'UG',
            'gscafb_business_name' => null,
            'gscafb_trading_name' => null,
            'gscafb_activity_description' => null,
            'gscafb_industry_sector_code' => null,
            'gscafb_date_registered' => null,
            'gscafb_business_type_code' => null,
            'gscafb_surname' => 'KAFEERO',
            'gscafb_forename1' => 'DICTIONER',
            'gscafb_forename2' => 'KAFEERO',
            'gscafb_forename3' => null,
            'gscafb_gender' => '0',
            'gscafb_marital_status' => '8',
            'gscafb_date_of_birth' => '19690808',
            'ei_employment_type' => '0',
            'ei_primary_occupation' => 'HEAD TEACHER',
            'ei_employer_name' => 'NAMUNYOLO PRIMARY SCHOOL',
            'ei_employee_number' => '42563',
            'ei_employment_date' => '20000602',
            'ei_income_band' => '13',
            'ei_salary_frequency' => '4',
            'pci_unit_number' => null,
            'pci_building_name' => null,
            'pci_floor_number' => null,
            'pci_plot_or_street_number' => null,
            'pci_lc_or_street_name' => 'UNKNOWN',
            'pci_parish' => 'BUWANGA WARD',
            'pci_suburb' => null,
            'pci_village' => 'UNKNOWN',
            'pci_county_or_town' => 'KITAMIRO BUVUMA',
            'pci_district' => 'KITAMIRO BUVUMA',
            'pci_region' => '2',
            'pci_po_box_number' => null,
            'pci_post_office_town' => null,
            'pci_country_code' => 'UG',
            'pci_period_at_address' => '3',
            'pci_flag_of_ownership' => 'R',
            'pci_primary_number_country_dialling_code' => '256',
            'pci_primary_number_telephone_number' => '752247927',
            'pci_other_number_country_dialling_code' => null,
            'pci_other_number_telephone_number' => '752247927',
            'pci_mobile_number_country_dialling_code' => '256',
            'pci_mobile_number_telephone_number' => '752247927',
            'pci_facsimile_country_dialling_code' => null,
            'pci_facsimile_number' => null,
            'pci_email_address' => null,
            'pci_web_site' => null,
            'pci_latitude' => null,
            'pci_longitude' => null,
            'sci_unit_number' => null,
            'sci_unit_name' => null,
            'sci_floor_number' => null,
            'sci_plot_or_street_number' => null,
            'sci_lc_or_street_name' => 'NAMUNYOLO PRIMARY SCHOOL',
            'sci_parish' => 'BUWANGA WARD',
            'sci_suburb' => '0',
            'sci_village' => 'BUWANGA',
            'sci_county_or_town' => '0',
            'sci_district' => 'BUVUMA',
            'sci_region' => '0',
            'sci_po_box_number' => null,
            'sci_post_office_town' => null,
            'sci_country_code' => 'UG',
            'sci_period_at_address' => '277',
            'sci_flag_for_ownership' => 'T',
            'sci_primary_number_country_dialling_code' => '256',
            'sci_primary_number_telephone_number' => '2247927',
            'sci_other_number_country_dialling_code' => null,
            'sci_other_number_telephone_number' => null,
            'sci_mobile_number_country_dialling_code' => null,
            'sci_mobile_number_telephone_number' => null,
            'sci_facsimile_country_dialling_code' => null,
            'sci_facsimile_number' => null,
            'sci_email_address' => null,
            'sci_web_site' => null,
            'sci_latitude' => null,
            'sci_longitude' => null,
            'amount_approved' => '0',
            'approved_currency' => null,
            'ii_refugee_number' => null,
            'ii_work_permit_number' => null,
        ];
    }
}
