<?php

namespace Database\Factories;

use App\Models\Validator\DataSubmission;
use App\Utilities\ParticipatingInstitutionsUtility;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Validator\DataSubmission>
 */
class DataSubmissionFactory extends Factory
{
    protected $model = DataSubmission::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $institutions = collect((new ParticipatingInstitutionsUtility)->all())->take(5)->all();
        $piCode = $this->faker->randomElement(array_keys($institutions));
        $currentDate = now()->format('Ymd');

        return [
            'pi_identification_code' => $piCode,
            'institution_name' => $institutions[$piCode],
            'submission_date' => '20231031',
            'version_number' => '8.0',
            'creation_date' => $currentDate,
            'datecreated' => $currentDate,
            'datemodified' => $currentDate,
            'file_identifier' => $this->faker->unique()->randomElement(array_keys(config('uploader.files'))),
            'validationstatus' => 0,
        ];
    }
}
